import os
import json
import re
from typing import Callable, TypeVar, Union, Dict, Tuple, Any
from pathlib import Path
import sys

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)


from Connections.search_db import SearchDb
from models.branch_id_acquirer import *
from models.refund_log import *
from services.telegram_bot_service import telegram_bot_sendtext
from Config.database import query_one_db, query_all_db
import Config.vars as vars
from logging import getLogger
from services.logger import create_logger
from functools import wraps


if __name__ == '__main__': 
    utils_logger = create_logger('')
    utils_logger.setLevel(20)
else: 
    utils_logger = create_logger(__name__, without_handler = True)

PIX_PAGA_LEVE_LABEL = 'pix pagale'



CARTAO = ['elo', 'visa', 'visa emv', 'visa cielo', 
        'electron','visa elect', 'electron emv', 'electron cielo', 
        'mastercard','mastercard deb',  'mastercard emv', 'mastercard cielo','mastercard safra', 
        'maestro', 'maestro emv', 'maestro cielo',
        'elo debito', 'elo credito','elo credit', 'valetik emv',
        'amex', 'amex cielo', 'american express', 'visa electron', 'caixa digital', 'hipercard', 'discover',
        "aura", "diners cie", "credi-shop", "credi shop", "credishop"]

REDE = ['hipercard l0500']

PIX = ['merc paggo','pix mercado pago','pix mercad','pix','pix vindi', 'pix itau', 'merc paggo pix', PIX_PAGA_LEVE_LABEL]

HIBRIDO = ['nupay']

WIND = ["WIND"]

GETNET_INVALIDOS = ['002', 
'003', '005', '008', '009', '010', '013', '014', '015', '023', '024', '025', '026', '028', '029', '030', '061', 
'062', '063', '064', '067', '069', '070', '102']

GETNET_VALIDOS = ['000', '020', '055', '072', '054']

CIELO_INVALIDOS = ['2','5','7','10','51','54','56','72','77','99','101','102','103','104','105','106',
'108','109','115','120','200','201','203','206','209','214','215','216','217','218','219','221','222','223','224','225',
'476','477','800','801','802','803','823','999']

CIELO_VALIDOS = ['1','17']



XML_MSG_GETNET_POST_CANCELAMENTO = '''
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:br="http://br.com.getnet.reqcanc.ws.service/">
        <soapenv:Header/>
        <soapenv:Body>
            <br:requestCancelTransation>
                <!--Optional:-->
                <arg0>
                    <authentication>
                        <username>{}</username>
                        <password>{}</password>
                    </authentication>
                    <cancelTransaction>
                        <branch>{}</branch>
                        <terminal>{}</terminal>
                        <autorization>{}</autorization>
                        <date>{}</date>
                        <modality>{}</modality>
                        <amount>{:.2f}</amount>
                        <currency_code>986</currency_code>
                        <inst_num>{}</inst_num>
                        <nsu>{}</nsu>
                        <cancel_amount>{:.2f}</cancel_amount>
                    </cancelTransaction>
                </arg0>
            </br:requestCancelTransation>
        </soapenv:Body>
        </soapenv:Envelope>
'''

XML_MSG_GETNET_RECEIVE = '''
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:br="http://br.com.getnet.reqcanc.ws.service/">
    <soapenv:Header/>
    <soapenv:Body>
        <br:queryCancelTransationByProtocol>
            <!--Optional:-->
            <arg0>
                <authentication>
                    <username>{}</username>
                    <password>{}</password>
                </authentication>
                <queryByProtocol>
                    <protocol>{}</protocol>
                </queryByProtocol>
            </arg0>
        </br:queryCancelTransationByProtocol>
    </soapenv:Body>
    </soapenv:Envelope>
'''


def get_application_root_path():
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def get_config():
    path = get_application_root_path()
    return json.load(open(path + '\\config.json'))

def is_a_mapped_payment_method(payment_method: str, estorno_service, refund_info: Dict):
    
    payment_method_simplified = get_tipo(payment_method)
    if payment_method_simplified != -1: return payment_method_simplified
    else:
        refund_info['motivo'] = f'Meio de pagamento não mapeado - {refund_info["bandeira"]}'
        refund_info['status'] = vars.STATUS_PENDENCY_FOR_CSC
        estorno_service.record_refund_processment(refund_info)
        telegram_bot_sendtext(f"Estorno {refund_info['idEstorno']}:Bandeira do cartao nao mapeada") 

    return -1
        
def get_tipo(bandeira:str):
    
    payment_method = bandeira.lower().strip()

    if payment_method in CARTAO:
        return 'cartao'   
    elif payment_method in PIX:
        return 'pix'    
    elif payment_method in HIBRIDO:
        return 'hibrido'    
    else:
        return -1


def get_app_root_path() -> (str):
        app_root_path = os.path.dirname(os.path.dirname(__file__))
        return app_root_path

def del_app_root_path():
    path_output = get_app_root_path()
    
    try:
        os.remove(path_output + '\output.csv')
    except Exception:
        pass


class FileDirHandler():

    def __init__(self, default_download_path):
        self.default_download_path = default_download_path                
        super().__init__()
    
    def create_dir(self, filepath = None):
        download_path = self.default_download_path
        filepath = download_path if not bool(filepath) else filepath               
        if (not os.path.exists(filepath)):
            os.mkdir(filepath)
        
    def clean_dir(self, filepath = None):
        download_path = self.default_download_path        
        filepath = download_path if not bool(filepath) else filepath
        for filename in os.listdir(filepath):
            try:
                os.remove('{}/{}'.format(filepath, filename))
            except Exception as e:
                raise e
    

def is_to_avoid_brand(current_brand: str) -> bool:
    """### Evitar finalizadores

    Nessa caso, não será gerado pendência para o CSC

    Args:
        current_brand (str): Finalizador de compra atual

    Returns:
        bool: Deve pular ou não
    """

    BRAND_BLACK_LIST = ("Marktplace", "ifood")

    for brand_to_avoid in BRAND_BLACK_LIST:

        if brand_to_avoid.lower() in current_brand.lower():
            return True

    return False


class ResultHandle():

    def __init__(self, success: bool, data, error_description: str, success_description=None):
        """ ### Controlar o fluxo de erros

        Args:
            success (bool): Booleano que marca se a operação ocorreu bem
            data (any): Resultado esperado
            error_description (str): Descriação do erro caso houver
            wait (bool): Deve
        """
        self.success = success
        self.success_description = success_description
        self.error_description = error_description
        self.data = data
        self.wait = False

    @property
    def failure(self):
        return not self.success

    def __str__(self):
        if self.success:
            return f'[Success]'
        else:
            return f'[Failure]: "{self.error_description}"'

    def __repr__(self):
        if self.success:
            return f"<Result success={self.success}>"
        else:
            return f'<Result success={self.success}, message="{self.error_description}">'

    @classmethod
    def Fail(cls, error_msg, data=None):
        return cls(False, data=data, error_description=error_msg, success_description=None)

    @classmethod
    def Ok(cls, data=None, success_description=None):
        return cls(True, data=data, success_description=success_description, error_description=None)

    @classmethod
    def Waiting(cls, error = None, data=None):
        cls_obj = cls(False, data=data, error_description=error, success_description=None)
        cls_obj.wait = True
        return cls_obj



T = TypeVar('T', bound=Callable[..., Any])
def exception_report(_func: T = None, *, logmsg: str = None, module_name: str = None)-> T:
    """ ## Informar qual foi a exeção que ocorreu de modo mais conciso

    Args:
        `_func` (Callable, optional): Caso não seja passado nenhum argumento-chave\n
        essa variável será a referência da função que será decorada. Se pelo menos \n
        1 argumento-chave por passado essa variável será igual a None \n \n

        `logmsg` (str, optional): Mensagem personalizada para a exeção \n
        `module_name` (str, optional): Módulo com o logger personalizado para emitir a messagem \n
    """

    def decorate_with_log(func: Callable[..., T]) -> Callable[..., Union[T, ResultHandle]]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Union[T, ResultHandle]:
            module = module_name if module_name is not None else func.__module__
            logger = getLogger(module)

            msg = logmsg if logmsg is not None else f"Erro na função {func.__name__}"
            
            try:
                value = func(*args, **kwargs)
                return value
            except Exception as error:
                logger.error(msg)
                raise error

        return wrapper
        

    if _func is None:
        return decorate_with_log
    else:
        return decorate_with_log(_func)


def enterprise_code_of_the_branch(branch_num: Union[int, str]) -> str:
    """Indicar qual é o código empresa para a filial

    Args:
        branch_num (Union[int, str]): Número da filial

    Returns:
        str: Código de da empresa: E.g. (`exf` ou `pm`)
    """        

    enterprise_code_query = """
    SELECT EMPR_CD_EMPRESA FROM Cosmos_v14b.dbo.FILIAL NOLOCK
    WHERE FILI_CD_FILIAL = ?
    """

    if isinstance(branch_num, str): branch_num = int(branch_num)

    try:
        enterprise_code = query_one_db(enterprise_code_query, branch_num)
        assert isinstance(enterprise_code, dict), f"Sem código de empresa para filial {branch_num}"
    except Exception as error:
        print(f"Erro ao verificar qual a empresa da filial {branch_num}")
        print(f"Detalhes: {error}")
        raise error

    if enterprise_code["EMPR_CD_EMPRESA"] == 2:
        return vars.EXTRA_FARMA_CASE
    else:
        return vars.PAGUE_MENOS_CASE

DIFF_VALUE_MARGIN = 1
class DiffValueError(Exception):
    def __init__(self, message):
        self.message = message

def verify_diff_values(obj_estorno_log: Dict, 
                      payment_method_value: float) -> Dict:    

    if obj_estorno_log["valorTotalVenda"] == obj_estorno_log["valorEstorno"]:

        return diff_values_in_a_total_refund(obj_estorno_log, float(payment_method_value))

    else:
        return diff_values_in_a_parcial_refund(obj_estorno_log, float(payment_method_value))


def diff_values_in_a_total_refund(obj_estorno_log: Dict, 
                      payment_method_value: float) -> Dict:
    """Verificar se houve divergência de valores entre troca-dev e outras fontes
    quando é um esotrno total. 
    
    Essa função existe para evitar o envio de um estorno total
    menor do que foi pedido pelo cliente dentro da margem de erro. 

    Essa situação ocorre quando vem do troca-dev um pedido de estorno total
    (valor do cupom igual ao valor do estorno) e ao verificar o valor do finalizador
    em outra fonte (ex: VTEX) vem um valor maior do que o susposto estorno total. 
    Dessa maneira, fica a dúvida, se é um estorno total, por que o valor do finalizador
    está diferente do finaliador, será que na realidade foi um estorno parcial?


    Args:
        obj_estorno_log (Dict): Dados do pedido de estorno
        payment_method_value (float): Valor do meio de pagamento no qual será feito o estorno

    Raises:
        Exception: _description_

    Returns:
        Dict: Dados do pedido de estorno
    """
    
    

    if abs(obj_estorno_log["valorTotalVenda"] - payment_method_value) <= DIFF_VALUE_MARGIN:
        obj_estorno_log['valorEstorno'] = payment_method_value

    else:
        raise DiffValueError("Divergência de valores entre troca-dev e outras fontes")
    
    return obj_estorno_log

def diff_values_in_a_parcial_refund(obj_estorno_log: Dict, 
                      payment_method_value: float) -> Dict:
    
    if ((obj_estorno_log["valorEstorno"] - payment_method_value > 0) 
    and (obj_estorno_log["valorEstorno"] - payment_method_value <= DIFF_VALUE_MARGIN)):
        
        obj_estorno_log['valorEstorno'] = payment_method_value

    if ((obj_estorno_log["valorEstorno"] - payment_method_value > 0) 
    and (obj_estorno_log["valorEstorno"] - payment_method_value > DIFF_VALUE_MARGIN)):
        
        raise DiffValueError("Divergência de valores entre troca-dev e outras fontes")

    return obj_estorno_log


def get_branch_id_in_acquirer(refund_info: EstornoLog , acquirer_code: int) -> Tuple[Union[vars.ResultHandle, BranchIdentificationInAcquirer], EstornoLog]:

    if acquirer_code not in (102, 108, 95):
        raise NotImplementedError(f"Não há ORM para a adquirente informada: {acquirer_code}")
    
    if acquirer_code == 95:
        result = BranchIdentificationInAcquirer(terminal = None, establishment = None, branch = refund_info.codigoFilialOrigem)
        return result, refund_info

    query_terminal_and_establishment = """
            select num_terminal, cod_estabelecimento, cod_filial
            from scope.clusterscopeCNF{db_cluster}.dbo.TerminalConfiguration nolock
            where 
            num_terminal <> ''
            AND cod_filial = '{branch_z_padded}'
            AND cod_rede = '{acquirer_code}'
            """

    if refund_info.codigoFilialOrigem <= 446: db_cluster = 1
    elif refund_info.codigoFilialOrigem > 446 and refund_info.codigoFilialOrigem <= 892: db_cluster = 2
    elif refund_info.codigoFilialOrigem > 892 and refund_info.codigoFilialOrigem <= 1338: db_cluster = 3
    elif refund_info.codigoFilialOrigem > 1338 and refund_info.codigoFilialOrigem <= 1530: db_cluster = 4
    if refund_info.codigoFilialOrigem >= 7000: db_cluster = 5

    branch = str(refund_info.codigoFilialOrigem).zfill(4)

    query = query_terminal_and_establishment.format(db_cluster = db_cluster,
                                                    branch_z_padded = branch,
                                                    acquirer_code = acquirer_code)
    
    branch_in_acquirer = query_all_db(query)

    if len(branch_in_acquirer) == 0:
        msg = f"Não foi encontrar os dados de terminal e estabelecimento para a filial {branch}"
        refund_info.motivo = msg
        refund_info.status = vars.STATUS_PENDENCY_FOR_CSC
        utils_logger.warning(msg)
        return vars.ResultHandle.Fail(msg), refund_info

    if len(branch_in_acquirer) > 1:
        utils_logger.warning("Mais de um registro de terminal e estabelecimento encontrados")
        utils_logger.warning("O primeiro será usado")


    if acquirer_code == 102:
        result = load_orm_branch_id_in_cielo_tef(branch_in_acquirer[0])
    if acquirer_code == 108:
        result = load_orm_branch_id_in_getnet_tef(branch_in_acquirer[0])

    return result, refund_info

def avoid_legacy_api_duplicates(id_refund: Union[int, str]) -> bool:

    finder = SearchDb()

    id_refund = str(id_refund) if isinstance(id_refund, int) else id_refund
    
    result, _ = finder.search_refund_by_refund_id(id_refund)
    if result is not None:
        utils_logger.info(f"Pedido de estorno {id_refund} ja foi processado")
        utils_logger.info("Duplicidade da consulta da API") 
        return True
    else:
        return False


if __name__ == "__main__":

    x = get_branch_id_in_acquirer(929, "102")


