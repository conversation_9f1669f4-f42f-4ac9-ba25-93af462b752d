import requests
import json
import time
# import schedule

bot_token = "5344840058:AAE3u0djRF-e8pd6RN9FvL0XamS7BG9sp6Y"
chat_id = -616729707

def telegram_bot_sendtext(msg):
    print(msg)
    try:
        data = {"chat_id": chat_id, "text": msg}
        url = "https://api.telegram.org/bot{}/sendMessage".format(bot_token)
        requests.post(url, data)
    except Exception as e:
        print("Erro no sendMessage:", e)
