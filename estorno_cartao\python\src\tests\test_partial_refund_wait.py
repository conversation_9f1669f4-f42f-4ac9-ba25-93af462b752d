import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime
import sys
from pathlib import Path

src_path = Path(__file__).parent.parent
sys.path.append(str(src_path))

from utils import is_there_a_partial_refund_to_wait
from models.result_handle import ResultHandle


class TestPartialRefundWait:
    
    @pytest.fixture
    def mock_current_refund(self):
        return {
            'codigoFilialOrigem': 123,
            'codigoAutorizacao': 'AUTH123'
        }
    
    @patch('utils.SearchDb')
    @patch('utils.datetime')
    def test_no_existing_refunds(self, mock_datetime, mock_search_db, mock_current_refund):
        # Setup
        mock_finder = MagicMock()
        mock_search_db.return_value = mock_finder
        
        # Mock the search function to return empty data (no existing refunds)
        mock_finder.is_there_already_a_refund_for_the_same_transaction.return_value = ResultHandle.Ok([])
        
        # Execute
        result = is_there_a_partial_refund_to_wait(mock_current_refund)
        
        # Assert
        assert result is False
        mock_finder.is_there_already_a_refund_for_the_same_transaction.assert_called_once_with(
            mock_current_refund['codigoFilialOrigem'], 
            mock_current_refund['codigoAutorizacao']
        )
    
    @patch('utils.SearchDb')
    @patch('utils.datetime')
    def test_search_failure(self, mock_datetime, mock_search_db, mock_current_refund):
        # Setup
        mock_finder = MagicMock()
        mock_search_db.return_value = mock_finder
        
        # Mock the search function to return failure
        mock_finder.is_there_already_a_refund_for_the_same_transaction.return_value = ResultHandle.Fail("Database error")
        
        # Execute
        result = is_there_a_partial_refund_to_wait(mock_current_refund)
        
        # Assert
        assert result is True  # Should return True on failure
    
    @patch('utils.SearchDb')
    @patch('utils.datetime')
    def test_recent_refund_less_than_one_day(self, mock_datetime, mock_search_db, mock_current_refund):
        # Setup
        mock_finder = MagicMock()
        mock_search_db.return_value = mock_finder
        
        # Set current date
        current_date = datetime(2025, 6, 10, 13, 0, 0)
        mock_datetime.today.return_value = current_date
        
        # Mock the search function to return a recent refund (less than 1 day old)
        refund_date = datetime(2025, 6, 10, 12, 30, 15)  # Just 30 minutes ago
        mock_finder.is_there_already_a_refund_for_the_same_transaction.return_value = ResultHandle.Ok([
            {'DATA_DEVOLUCAO': refund_date}
        ])
        
        # Execute
        result = is_there_a_partial_refund_to_wait(mock_current_refund)
        
        # Assert
        assert result is True  # Should wait for recent refund
    
    @patch('utils.SearchDb')
    @patch('utils.datetime')
    def test_refund_more_than_one_day(self, mock_datetime, mock_search_db, mock_current_refund):
        # Setup
        mock_finder = MagicMock()
        mock_search_db.return_value = mock_finder
        
        # Set current date
        current_date = datetime(2025, 6, 11, 13, 0, 0)
        mock_datetime.today.return_value = current_date
        
        # Mock the search function to return an older refund (more than 1 day old)
        refund_date = datetime(2025, 6, 9, 12, 3, 15)  # More than 1 day ago
        mock_finder.is_there_already_a_refund_for_the_same_transaction.return_value = ResultHandle.Ok([
            {'DATA_DEVOLUCAO': refund_date}
        ])
        
        # Execute
        result = is_there_a_partial_refund_to_wait(mock_current_refund)
        
        # Assert
        assert result is False  # Should not wait for older refund

if __name__ == "__main__":
    pytest.main([__file__])