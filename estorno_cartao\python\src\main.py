import utils
import sys
from executers.main_pos_extrafarma import POSEX
from executers.main_pos import POS 
from executers.main_tef import TEF
from executers.main_tef_extrafarma import TEFEX
from executers.main_retorno import RetornoRequisicao
from executers.main_atualizar_token import AtualizarToken
from services.telegram_bot_service import *
from services.nupay.nupay_backward import NupayTracker
from services.itau.itau_return import ItauReturn
from services.credshop.credshop_tracker import CredshopTracker
from services.logger import create_logger
from services.zendesk.ticket_follow_up import verify_not_solved_tickets

# POINT OF SALE
################################
# Argumento 0: pos pmenos      #
# Argumento 1: tef pmenos      #
# Argumento 2: retorno Cielo   #
# Argumento 3: retorno Getnet  #
# Argumento 4: tef Extrafarma  #
# Argumento 5: pos Extrafarma  #
# Argumento 6: Atl.Token Cielo #
# Argumento 7: Retorno Nupay   #
# Argumento 8: Retorno Itau    #
# Argumento 9: Verificar tickets zendesk #
# Argumento 10: Retorno Credshop#
################################

main_logger = create_logger('', "logs.log")
main_logger.setLevel(20)

if __name__ == '__main__':  
    
    if len(sys.argv) > 1:
        option = int(sys.argv[1]) 
    else:
        option = 8

    app_config = utils.get_config()  

    # Definindo variáveis para argumentos 
    ecommerce_v2 = 'ENV_ECO_V2' 
    pdv_v2 = 'ENV_PDV_V2'
    retorno_v2 = 'RETORNO_V2'

    # Arquivo de comunicacao com o Pentaho

    # TODO: Gerar log da excecao lancada pelo script, no banco
    try:

        if option == 0:
            
            # POS
            main_ecommerce = POS(app_config, option)                   
            main_ecommerce.execute_process()

        elif option == 1:

            # TEF
            TEF(app_config, option).execute_process()

        elif option == 2:        

            retorno_service = RetornoRequisicao(app_config)
            # RETORNO CIELO
            
            retorno_service.processar_retorno_cielo()

        elif option == 3:
            retorno_service = RetornoRequisicao(app_config)
            retorno_service.processar_retorno_getnet()    


        elif option == 4:

            # TEF
            TEFEX(app_config, option).execute_process()  
        
        elif option == 5:

            # TEF
            POSEX(app_config, option).execute_process()  

        elif option == 6:

            # TEF
            AtualizarToken(app_config).execute()

        elif option == 7:
            nupay_tracker = NupayTracker(app_config)
            nupay_tracker.execute()
        
        elif option == 8:
            itau_return = ItauReturn(app_config)
            itau_return.verificar_estornos_em_andamento()
        
        elif option == 9:
            verify_not_solved_tickets()
        
        elif option == 10:
            credshop_tracker = CredshopTracker()
            credshop_tracker.track_credshop_refunds()
            
    

    # Tratando exessões do processo
    except Exception as e:

        if option == 0:
            msg = 'ESTORNO VENDA - ERRO POS:\n{}'.format('error {} - desc: {}'.format(e.__class__, e))

            if (msg != "ESTORNO VENDA - ERRO POS:\nerror <class 'ValueError'> - desc: Erro na requisicao: status 404"):

                telegram_bot_sendtext(msg)
        
        if option == 1:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO TEF:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 2:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 3:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO GET:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 4:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO TEF extrafarma:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 5:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO POS extrafarma:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 6:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO AO ATUALIZAR TOKEN:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 7:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO NUPAY:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
           
        if option == 8:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO ITAU:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
            
        if option != 1:
            msg = 'ESTORNO VENDA - ERRO TEF:\n{}'.format('error {} - desc: {}'.format(e.__class__, e))

            if (msg != "ESTORNO VENDA - ERRO TEF:\nerror <class 'ValueError'> - desc: Erro na requisicao: status 404"):

                telegram_bot_sendtext(msg)
            
        raise e   
    