from decimal import Decimal
from datetime import datetime, timedelta
import requests
from Config import vars
from typing import Dict, List
from Config.database import query_all_db
from services.estorno_service import EstornoService
from pathlib import Path
import sys
from Connections.search_db import SearchDb
from models.refund_log import estorno_venda_log_orm

class ItauReturn:

    def __init__(self, config: dict):
        self.config = config
        self.__api_url_consulta = config['itau']['api_consulta_estorno_url']
        self.__api_url_token = config['itau']['api_token_url']
        self.client_id_pm = config['itau']['client_id_pm']
        self.client_secret_pm = config['itau']['client_secret_pm']
        self.client_id_ef = config['itau']['client_id_ef']
        self.client_secret_ef = config['itau']['client_secret_ef']
        self.tokens = {'Pague Menos': None, 'Extrafarma': None}

    
    def verificar_estornos_em_andamento(self):
        try:
                print("Itau Return - verificar_estornos_em_andamento()")
                estorno_service = EstornoService(self.config)
                estornos_em_andamento = self.obter_estornos_em_andamento()
                finder = SearchDb()
                
                for estorno in estornos_em_andamento:
                    
                    idpix = finder.search_id_pix(estorno['ID_ESTORNO'])

                    resultado = self.consulta_pix(idpix, id_estorno=estorno['ID_ESTORNO'], nome_empresa=estorno['NOME_EMPRESA'])
                    
                    estorno_atualizado = self.atualizar_obj_estorno_log(estorno, resultado)
                    estorno_service.update_refund_processment(estorno_venda_log_orm(estorno_atualizado))

        except Exception as e:
                print(f"Erro ao verificar estorno ID {estorno['id']}: {e}")
  
    def obter_estornos_em_andamento(self, days_to_go_back: int = 90) -> List[Dict]:
        
        waiting_itau_query = f"""
        SELECT * FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG (nolock)
        WHERE 
        DATA_CADASTRO >= (GETDATE() - {days_to_go_back})
        AND DATA_CADASTRO < (GETDATE() + 1)
        AND STATUS LIKE '%EM ANDAMENTO ITAU%'
        ORDER BY DATA_CADASTRO DESC"""

        formatted_query = waiting_itau_query.format(days_to_go_back)
        try:
            waiting_itau_refunds = query_all_db(formatted_query)
        except Exception as error:
            print(f"Erro ao buscar informções de estornos do Itaú em espera")
            print(f"Detalhes: {error}")
            raise error

        return waiting_itau_refunds
    
    def consulta_pix(self, id_pix: str, id_estorno: str, nome_empresa: str) -> vars.ResultHandle:
        try:
            certificado = self.selecionar_certificado(nome_empresa)

            if self.tokens[nome_empresa] is None:
                self.tokens[nome_empresa] = self.obtertoken(nome_empresa, certificado)

            url_consulta = self.__api_url_consulta.format(id_pix, id_estorno)
            headers = {'Authorization': f'Bearer {self.tokens[nome_empresa]}'}

            response = requests.get(url_consulta, headers=headers, cert=certificado)
            if response.status_code == 403:
                self.tokens[nome_empresa] = self.obtertoken(nome_empresa)
                headers['Authorization'] = f'Bearer {self.tokens[nome_empresa]}'
                response = requests.get(url_consulta, headers=headers, cert=certificado)

            return self.tratar_resposta_estorno(response)

        except Exception as e:
            print(f'Erro ao consultar o pix: {str(e)}')
            return vars.ResultHandle.Fail(f'Erro ao consultar estorno na API')
    
    def tratar_resposta_estorno(self, response) -> vars.ResultHandle:
        if response.status_code == 204:
            return vars.ResultHandle.Waiting("Consulta estorno não retornou nenhum conteúdo.")
       
        if response.status_code == 200:
            status, motivo = response.json().get('status'), response.json().get('motivo')
            if status == 'DEVOLVIDO':
                return vars.ResultHandle.Ok(motivo)
            elif status == 'NAO_REALIZADO':
                return vars.ResultHandle.Fail(motivo)
            elif status == 'EM_PROCESSAMENTO':
                return vars.ResultHandle.Waiting(motivo)
            else:
                return vars.ResultHandle.Fail('Erro na API: Status não mapeado')
            
        status_message = {
            400: "Parâmetros incorretos ou ausentes",
            403: "Requisição de participante autenticado que viola alguma regra de autorização.",
            404: "Recurso solicitado não foi encontrado",
            500: "Erro inesperado. Entre em contato com suporte Itaú",
            503: "Serviço indisponível"
        }.get(response.status_code, 'Erro desconhecido na API')

        return vars.ResultHandle.Fail(status_message)

    def obtertoken(self, nome_empresa, certificado) -> str:
        try:
            client_id, client_secret = (self.client_id_ef, self.client_secret_ef) if nome_empresa == 'Extrafarma' else (self.client_id_pm, self.client_secret_pm)
            data = {
                'client_id': client_id,
                'client_secret': client_secret,
                'grant_type': 'client_credentials'
            }

            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            response = requests.post(self.__api_url_token, data=data, headers=headers, cert=certificado)
            
            return response.json().get('access_token') if response.status_code == 200 else None

        except Exception as e:
            print(f'Erro inesperado ao obter token: {str(e)}')
            return None    

    def atualizar_obj_estorno_log(self, obj_estorno_log, result, status_sucesso='FINALIZADO', status_espera='EM ANDAMENTO ITAU', status_erro=vars.STATUS_PENDENCY_FOR_CSC):
    
        if result.success:
            motivo = 'REALIZADO'
            status = status_sucesso
            obj_estorno_log['DATA_RESP_ADQUIRENTE'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        elif result.wait:
            register_datetime = obj_estorno_log['DATA_CADASTRO']
            retry_datetime = register_datetime + timedelta(days=1)


            if datetime.today() < retry_datetime:
                motivo = 'Estorno pix Itaú em processamento'
                status = status_espera
            else:
                motivo = 'Pedido de estorno ultrapassou o limite de espera'
                status = status_erro
        else:
            motivo = result.error_description
            status = status_erro

        obj_estorno_log['MOTIVO'] = motivo[:255]
        obj_estorno_log['STATUS'] = status

        return obj_estorno_log
    
    def mapping_bd_columns_to_legacy_api(self, refund_info: Dict):
        """Mapear objeto das informações de estorno no grupo pmenos
        para poder aproveitar endpoint de atualizar informações 
        de estorno

        Args:
            refund_info (Dict): informações de estorno no grupo pmenos

        Returns:
            Dict: Objesto mapeado para input da API
        """

        mapping = {
        "FILIAL_ORIGEM": "codigoFilialOrigem",
        "FILIAL_DESTINO": "codigoFilialDestino",
        "NUMERO_CUPOM": "NumeroCupom",
        "NUMERO_COO": "NumeroCoo",
        "PDV_DEVOLUCAO": "PdvInformado",
        "NOME_CLIENTE": "NomeCliente",
        "NUMERO_VTEX": "NumeroPedidoVTEX",
        "NUMERO_DELIVERY": "NumeroPedidoDelivery",
        "DATA_MOVIMENTO": "DataMovimento",
        "VALOR_CUPOM": "ValorTotalVenda",
        "VALOR_ESTORNO": "ValorEstorno",
        "DATA_DEVOLUCAO": "DataDevolucao",
        "NSU_TEF": "NSUTEF",
        "NSU_HOST": "NSUHOST",
        "BANDEIRA": "Bandeira",
        "TELEFONE_CLEINTE": "Telefone",
        "EMAIL_CLEINTE": "Email",
        "NUMERO_PRE_VENDA": "NumeroPreVenda",
        "E_PLANO_ASSINATURA": "PlanoAssinatura",
        "CANAL_VENDAS": "CanalVendas",
        "STATUS": "Status",
        "MOTIVO": "Motivo",
        "STATUS_BPM": "StatusBPM",
        "MOTIVO_BPM": "MotivoBPM",
        "NUMERO_CARTAO_SEMPRE": "CartaoSempre",
        "FLDE_QT_PAR_CAR": "Parcelas",
        "FLDE_TP_POS": "Tipo",
        "E_PAGAMENTO_UNIFICADO": "FlagPagamentoUni",
        "NSU_POS": "NsuPos",
        "ID_DEVTROCA_CAB": "IdDevtrocaCab",
        "COO_PAGAMENTO_UNIFICADO": "CooPagUni",
        "ID_ESTORNO_CAB": "NomeArquivo",
        "ID_INST_BPM": "NumeroInstancia",
        "PROTOCOLO_GETNET": "ProtocoloGetnet",
        "DATA_ALTERACAO": "DataAlteracao",  
        "DATA_VENDA": "DataVenda",
        "T_ID": "TID",
        "CODIGO_AUTORIZAÇÂO": "CodigoAutorizacao",
        "DATA_CADASTRO": "DataCadastro",
        "NOME_EMPRESA": "NomeEmpresa",
        "PROTOCOLO_CIELO": "ProtocoloCielo",
        }

        new_dict = dict()

        for key, value in refund_info.items():

            if key not in tuple(mapping.keys()): continue

            if isinstance(value, datetime):
                value = value.strftime("%Y-%m-%d")
            if isinstance(value, Decimal):
                value = int(value)

            new_dict[mapping[key]] = value

        return new_dict 

    def selecionar_certificado(self, nome_empresa: str) -> tuple:
        src_folder = local_execution_with_others_projects_modules(__file__, 3)
        certificados = {
            'Pague Menos': (src_folder.__str__() + r'\certificate\certificado_pm.crt', src_folder.__str__() + r'\certificate\chave_pm.key'),
            'Extrafarma': (src_folder.__str__() + r'.\certificate\certificado_ef.crt', src_folder.__str__() + r'.\certificate\chave_ef.key'),
        }       
        return certificados.get(nome_empresa, (None, None))

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n

    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """

    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder