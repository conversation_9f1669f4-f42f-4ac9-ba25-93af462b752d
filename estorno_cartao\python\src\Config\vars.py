import json 
from pathlib import Path

#Variável que representa o ambiente 
ENV = "prod"
src_folder = "C:\\RPA_EstornoV3_PDI\\estorno_cartao\\python\\src"
path_elements = src_folder + "\\Files\\elements"


CONN = json.load(open(src_folder + '\\Config\\connections.json'))

ISO_DATE_FORMAT = '%Y-%m-%dT%H:%M:%S%z'
DATE_BASE_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S.%f'
BASE_MSG = "id_Estorno_fin {id_estorno}: "

EXTRA_FARMA_CASE = "exf"
PAGUE_MENOS_CASE = "pm"

GETNET_ACQUIRE_NAME = 'GETNET'
GETNET_ACQUIRE_SCOPE_CODE = 108
CIELO_ACQUIRE_NAME = 'CIELO'
CIELO_ACQUIRE_SCOPE_CODE = 102
CREDSHOP_ACQUIRE_NAME = 'CREDI-SHOP'
CREDSHOP_ACQUIRE_SCOPE_CODE = 95
NUPAY_ACQUIRE_NAME = 'NUPAY'

PIX_GENERAL_NAME = 'pix'

STATUS_PENDENCY_FOR_CSC = "Direcionado ao CSC"
STATUS_REFUND_PROCESS_END =  "Estorno finalizado"

STEM_FOR_STATUS_WAIT_ACQUIRE = "Estorno solicitado"
STATUS_WAIT_ITAU = f"{STEM_FOR_STATUS_WAIT_ACQUIRE} a ITAU"
STATUS_WAIT_NUPAY =  f"{STEM_FOR_STATUS_WAIT_ACQUIRE} a {NUPAY_ACQUIRE_NAME}"
STATUS_WAIT_GETNET = f'{STEM_FOR_STATUS_WAIT_ACQUIRE} a {GETNET_ACQUIRE_NAME}'
STATUS_WAIT_CIELO = f'{STEM_FOR_STATUS_WAIT_ACQUIRE}  a {CIELO_ACQUIRE_NAME}'
STATUS_WAIT_CREDSHOP = f'{STEM_FOR_STATUS_WAIT_ACQUIRE} a {CREDSHOP_ACQUIRE_NAME}'

STATUS_WAIT_PROOF_OF_REFUND = 'Estorno Aprovado'
STATUS_PENDING_PHARMACY = 'Resposta de loja pendente'
STATUS_EXPIRED_TICKET = 'Chamado encerrado'

SUCCESSFUL_REFUND_REASON = "Realizado com sucesso"
DUPLICATED_REFUND_REASON = "Estorno duplicado"
PAYMENT_METHOD_FULLY_REFUNDED_REASON = "Finalizador já está totalmente estornado"


REFUND_PROCESS_END_OBSERVATION_PIX = "Compras Pix, o valor pode ser estorno em até 24 horas. Em caso de dúvidas, oriente o cliente a entrar em contato com o banco" 
WAIT_OBSERVATION = "Solicitamos o cancelamento de sua compra, o prazo de cancelamento é de até 24 horas"
PROOF_OF_REFUND_OBSERVATION = "Seu cancelamento foi aprovado! Sua carta será emitida dentro de 24 horas."
PENDENCY_FOR_CSC_OBSERVATION = "A situação foi encaminhada para o time o CSC solicitar o estorno manualmente."
REFUND_PROCESS_END_OBSERVATION_CARD = """Compras em crédito: O valor pode ser estornado ao cartão em até duas faturas.
Compras em débito: O prazo pode variar de acordo com o banco, geralmente ocorre em até
7 dias úteis.
Em caso de dúvidas, entre em contato com a central de atendimento, cujo número está no
verso do cartão."""

REFUND_PROCESS_END_OBSERVATION_NUPAY = """
Compras em crédito: O valor pode ser estornado ao cartão em até duas faturas.
Débito direto em conta: o valor pode ser estorno em até 24 horas. Em caso de dúvidas, oriente o cliente a entrar em contato com o banco
"""
PENDING_PHARMACY_OBSERVATION = """O chamado foi pendenciado por falta de informação, a loja deve consultar o ticket no Zendesk para responder a pendência
dentro de 48 horas ou o chamado será encerrado.
"""
EXPIRED_TICKET_OBSERVATION = """O chamado foi encerrado por falta de resposta da loja"""


MANUAL_PROCESSMENT_STATUS_EXPIRED_TICKET = "Ticket finalizado por inatividade"
MANUAL_PROCESSMENT_MACRO_SUCESSFUL_PATTERN = "Segue carta de cancelamento"
MANUAL_PROCESSMENT_MACRO_WAIT_PROOF_OF_REFUND = "disponível em até 2 dias úteis"


REQUEST_STAGE = "REQUEST_STAGE"
RETURN_STAGE = "RETURN_STAGE"
HYBRID_CASE_DEBIT = " Débito"
HYBRID_CASE_CREDIT = ' Crédito'




class ResultHandle():

    def __init__(self, success: bool, data, error_description: str):
        """ ### Controlar o fluxo de erros

        Args:
            success (bool): Booleano que marca se a operação ocorreu bem
            data (any): Resultado esperado
            error_description (str): Descriação do erro caso houver
            wait (bool): Deve
        """
        self.success = success
        self.error_description = error_description
        self.data = data
        self.wait = False

    @property
    def failure(self):
        return not self.success

    def __str__(self):
        if self.success:
            return f'[Success]'
        else:
            return f'[Failure]: "{self.error_description}"'

    def __repr__(self):
        if self.success:
            return f"<Result success={self.success}>"
        else:
            return f'<Result success={self.success}, message="{self.error_description}">'

    @classmethod
    def Fail(cls, error, data=None):
        return cls(False, data=data, error_description=error)

    @classmethod
    def Ok(cls, data=None):
        return cls(True, data=data, error_description=None)

    @classmethod
    def Waiting(cls, error = None, data=None):
        cls_obj = cls(False, data=data, error_description=error)
        cls_obj.wait = True
        return cls_obj


