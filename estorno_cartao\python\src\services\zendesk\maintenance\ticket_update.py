from pathlib import Path
import sys
from typing import Dict
from datetime import datetime
from time import sleep
from urllib3.connectionpool import log as urlliblogger
import pandas as pd

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 4)

from services.logger import create_logger
from services.zendesk.api_requests import update_ticket, get_ticket_info_with_rate_limit
from services.zendesk.zen_var import ENDING_STATUS, TICKET_CLOSED_STATUS, REMAINING_PERCENTAGE_TO_BE_ABOVE_OF
from Connections.search_db import SearchDb
from Connections.update_db import UpdateDb
from models.refund_log import estorno_venda_log_orm

log_level = 20
urlliblogger.setLevel(log_level)

if __name__ == '__main__': 
    ticket_update_logger = create_logger('')
    ticket_update_logger.setLevel(log_level)
else: 
    ticket_update_logger = create_logger(__name__, without_handler = True)

WAIT_REQUESTTIME_SECONDS = 62

if __name__ == "__main__":

    # 366348 cartão prd
    # 366169 pix prd
    # solved 
    # new 
    prd_env = "prd" 
    homo_env = "homo" 
    ticket_id_prd = 1541949 #ticket_id_prd = 1523112
    ticket_id_homo = 7425
    ticket = 7427
    

    HTML_BODY = """
<p>Prezado(a), transação cancelada pela adquirente.</p>
<p>
  Carta de cancelamento <strong>estará disponível em até 2 dias úteis no link abaixo</strong>:
</p>
<p>
  <a href="https://pmenos.fluig.cloudtotvs.com.br/portal/1/carta-cancelamento">
    https://pmenos.fluig.cloudtotvs.com.br/portal/1/carta-cancelamento
  </a>
</p>
<p>
  <strong><em>Após o processo de cancelamento em nosso sistema, o reembolso para o cliente depende dos prazos estabelecidos pela administradora do cartão de crédito/débito. Nos casos de PIX, o reembolso é imediato.</em></strong>
</p>
<p>
  <strong><em>Sugerimos orientar o cliente a entrar em contato com o emissor de seu cartão (o número fica no verso do cartão) para questionamentos sobre o prazo de devolução.</em></strong>
</p>
<p>
  Em caso de dúvidas, permanecemos à disposição nos seguintes canais:<br>
  Whatsapp: (85) 99761-6870<br>
  Telefone: (85) 3255-5491 / (85) 3255-5493<br>
  E-mail: <a href="mailto:<EMAIL>"><EMAIL></a>
</p>
<p>Atenciosamente,</p>
"""
    

    update_payload = {
            "ticket": {
                "comment": {
                    "html_body": HTML_BODY, #simple comment
                    "public": True
                            },
                "status": TICKET_CLOSED_STATUS,
                "locked":True
                    }
            }
    
    import pandas as pd 
    excel_path = Path(__file__).parent / "temp_maintance_files" / "Tickets_Encerrar_01.06.xlsx"
    data_base  = pd.read_excel(excel_path, sheet_name="ENCERRAR_AÇÃO_RPA")

    TICKET_ID_COLUMN = "ID"
    CUSTOM_RESPONSE_COLUMN = "Resposta"
    
    for i in range(data_base.shape[0]):

        ticket_update_logger.info(f"Atualizando ticket {data_base.at[i, TICKET_ID_COLUMN]}")
        response, response_headers = get_ticket_info_with_rate_limit(data_base.at[i, TICKET_ID_COLUMN], env=prd_env)

        if response.failure:
            ticket_update_logger.info(f"Erro ao verificar ticket {data_base.at[i, TICKET_ID_COLUMN]}")
            ticket_update_logger.info(f"Detalhes do erro: {response.error_description}")
            continue

        ratelimit = int(response_headers["ratelimit-limit"][1])
        ratelimit_remaining = int(response_headers["ratelimit-remaining"][1])

        if ratelimit_remaining < (REMAINING_PERCENTAGE_TO_BE_ABOVE_OF * ratelimit): 
            ticket_update_logger.info(f"Limite de requisições atingido, aguardando {WAIT_REQUESTTIME_SECONDS} segundos")
            sleep(WAIT_REQUESTTIME_SECONDS)

        if response.data["ticket"]["status"] in ENDING_STATUS:
                ticket_update_logger.info(f"Ticket {data_base.at[i, TICKET_ID_COLUMN]} já está encerrado")
                continue
        
        #update_payload["ticket"]["comment"]["html_body"] = data_base.at[i, CUSTOM_RESPONSE_COLUMN]
        response, response_headers = update_ticket(data_base.at[i, TICKET_ID_COLUMN], payload=update_payload, env=prd_env)

        if response.failure:
            ticket_update_logger.info(f"Erro ao atualizar ticket {data_base.at[i, TICKET_ID_COLUMN]}")
            ticket_update_logger.info(f"Detalhes do erro: {response.error_description}")
            continue

        ratelimit = int(response_headers["ratelimit-limit"][1])
        ratelimit_remaining = int(response_headers["ratelimit-remaining"][1])

        if ratelimit_remaining < (0.5 * ratelimit): 
            ticket_update_logger.info(f"Limite de requisições atingido, aguardando {WAIT_REQUESTTIME_SECONDS} segundos")
            sleep(WAIT_REQUESTTIME_SECONDS)
        
        ticket_update_logger.info(f"Ticket {data_base.at[i, TICKET_ID_COLUMN]} atualizado para {TICKET_CLOSED_STATUS}")


    ticket_update_logger.info("fim")



