

import requests
import json
from datetime import datetime 

class TrackingServices():

    def __init__(self, config:dict) -> str:
        
        self.__api_base_url = config['api']['movimento-bpm']['api_base_url']
        self.__resource = 'Tracking'
        super().__init__()
    
    def post_tracking(self, venda:dict, status:str, step:str='Estorno solicitado', content:str=None) -> str:        
        _url = self.__api_base_url.format(self.__resource)
        response = requests.post(url=_url, json=self.__montar_body_tracking(venda, status, step, content))
        if not response.ok:
            raise ValueError('Erro na requisicao: status {}'.format(response.status_code))
        return response.text

    def __montar_body_tracking(self, dados_estorno:dict, status:str, step:str='Estorno solicitado', content:str=None) -> dict:                                        
        
        def gerar_uid(numero_cupom, filial, data_movimento:str):            
            if '/' in data_movimento: data_movimento = data_movimento
            else:
                _data_aux = data_movimento.split('-')
                data_movimento = '{}/{}/{}'.format(_data_aux[2], _data_aux[1], _data_aux[0])             
            uid = 'EST-{}-{}-{}'.format(str(filial), str(numero_cupom), data_movimento.replace('/', '')) 
            return uid
              
        body = {}        
        _numero_cupom = dados_estorno['numeroCupom']
        _filial = dados_estorno['codigoFilial']        
        _data_movimento = dados_estorno['dataMovimento'] if 'dataMovimento' in dados_estorno else dados_estorno['dataVenda'] 
        # try: _data_tracking = str(datetime.strptime(dados_estorno['dataDevolucao'], '%d/%m/%Y')) if status == 'SOLICITADO' else str(datetime.today())
        try: _data_tracking = str(datetime.strptime(dados_estorno['dataDevolucao'], '%Y-%m-%dT%H:%M:%S.%f')) if status == 'SOLICITADO' else str(datetime.today())
        except: _data_tracking = str(datetime.today())

        body['uid'] = gerar_uid(_numero_cupom, _filial, _data_movimento)
        body['domain'] = 'Jornada de Vendas'
        body['step'] = step
        body['subProcess'] = 'Estorno de vendas'
        body['content'] =  body['uid'] if not bool(content) else content
        body['status'] = status
        body['dateTime'] = _data_tracking        
        print(body['uid'])
                
        return body