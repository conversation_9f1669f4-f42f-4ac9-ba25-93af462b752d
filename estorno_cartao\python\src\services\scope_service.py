from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import NoSuchElementException
from selenium import webdriver
from datetime import datetime

import time
import utils
import csv
import os

import json

class ScopeService():

    def __init__(self, config, driver):

        
        self.driver = driver

        self.config = config

        self.__initialize()
        super().__init__()


    def retrieve_data_scope(self, numero_cupom:str, filial:int, data_venda:str) -> None:
        
        
        self.__selecionar_banco_pesquisa(filial)
        
        self.__acessar_relatorios()
        
        self.__download_relatorio(filial=filial, data_venda=data_venda)
        
        vendas = self.__parse_csv_vendas() 
        venda_localizada = self.__find_venda_scope(numero_cupom, vendas)         

        if venda_localizada == {}:
            return -1
        # elif venda_localizada['status_venda'] == 'cancelado':
            # return -2
        elif venda_localizada['adquirente'] not in self.config['portal']['adquirentes']:
            return -3      

        return venda_localizada
    
        

    def __initialize(self) -> None:
         # Acessando portal 
        self.__login_scope()
        

    def __find_venda_scope(self, numero_cupom:str, list_vendas:list) -> dict:

        dados_venda = {}

        for venda in list_vendas:
            if not bool(venda[16]): continue            
            _numero_cupom = int(venda[16]) 
            situacao = venda[10].lower()            
            if int(numero_cupom) == _numero_cupom and situacao in ['ok', 'cancelado']:                
                dados_venda['data_venda'] = venda[0]
                dados_venda['data_deposito'] = venda[0]
                _lote = venda[0].split('/')
                _lote = '{}{}{}'.format(_lote[2][2:], _lote[1], _lote[0])
                dados_venda['lote'] = _lote
                dados_venda['adquirente'] = venda[4].lower()
                dados_venda['bandeira'] = venda[5].lower()
                dados_venda['valor_compra'] = float(venda[6].replace('.', '').replace(',', '.'))
                dados_venda['numero_cartao'] = venda[7].replace('-', '******')
                dados_venda['nsu'] = venda[9]
                dados_venda['nsu_host'] = venda[14]
                dados_venda['codigo_autorizacao'] = venda[15]
                dados_venda['status_venda'] = situacao 
                dados_venda['seq_controle'] = venda[17]              
                break            

        return dados_venda
                                    

    def __login_scope(self) -> None:
        _config_acesso_scope = self.config['portal']['scope']
        self.driver.get(_config_acesso_scope['url'])
        self.driver.find_element_by_id('formLogin:nomeUsuario').send_keys(_config_acesso_scope['user'])
        self.driver.find_element_by_id('formLogin:password').send_keys(_config_acesso_scope['password'])
        self.driver.find_element_by_id('formLogin:btnLogin').click()

    def __logout_scope(self) -> None:
        WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.LINK_TEXT, 'Sair'))).click()


    def __selecionar_banco_pesquisa(self, filial: int, opt:int=1) -> None:
        
        if opt == 1:
            WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.LINK_TEXT, 'Servidor'))).click()
            #WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.LINK_TEXT, 'HISTORICO'))).click()

            if filial >= 1 and filial <= 446:
                time.sleep(3)
                WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.LINK_TEXT, 'Grupo de Lojas 1 a 446'))).click()
            elif filial >= 447 and filial <= 892:
                time.sleep(3)
                WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.LINK_TEXT, 'Grupo de Lojas 447 a 892'))).click()
            elif filial >= 893 and filial <= 1338:
                time.sleep(3)
                WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.LINK_TEXT, 'Grupo de Lojas 893 a 1338'))).click()

        elif opt == 2: pass        
        else: pass


    def __acessar_relatorios(self) -> None:        
        WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.LINK_TEXT, 'Administrativo'))).click()
        WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.LINK_TEXT, 'Consulta operacional'))).click()


    def __download_relatorio(self, filial:str, data_venda:str) -> None:

        self.driver.find_element_by_id('form:periodoInicial_input').click()
        self.driver.find_element_by_id('form:periodoInicial_input').clear()
        self.driver.find_element_by_id('form:periodoInicial_input').send_keys(data_venda)

        self.driver.find_element_by_id('form:periodoFinal_input').click()
        self.driver.find_element_by_id('form:periodoFinal_input').clear()
        self.driver.find_element_by_id('form:periodoFinal_input').send_keys(data_venda)

        WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.ID, 'form:comboBoxLoja_label'))).click()
        
        self.__select_filial(filial=filial)

        ## Verificar se da certo ##
        # Marcando NSU_HOST
        # self.driver.find_element_by_id('form:comboBoxCamposPesquisa_label').click()
        # WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.ID, 'form:comboBoxCamposPesquisa_2'))).click()        
        self.driver.find_element_by_id('form:btPesquisar_button').click()

        # Verificando se existem vendas
        time.sleep(2)
        # if not self.__check_dados(): return -1 

        # Marcando Autorizacao
        self.driver.find_element_by_id('form:comboBoxCamposPesquisa_label').click()
        WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.ID, 'form:comboBoxCamposPesquisa_12'))).click()        
        self.driver.find_element_by_id('form:btPesquisar_button').click()

        # Verificando se existem vendas
        time.sleep(2)
        # if not self.__check_dados(): return -1            
        
        self.driver.find_element_by_id('form:btPesquisar_menuButton').click()     
        WebDriverWait(self.driver, 60).until(EC.presence_of_element_located((By.ID, 'form:cvs'))).click()
        
        utils.wait_download(self.config['firefox']['default_download_path'])

        # return 1

    def __select_filial(self, filial) -> None:
                
        filial = self.__format_filial(filial=filial)
        qtd_filials = self.driver.execute_script('return document.getElementById("form:comboBoxLoja_items").children.length')

        for i in range (int(qtd_filials)):            
            valor_combobox = self.driver.execute_script('return document.getElementById("form:comboBoxLoja_items").children[{}].innerText'.format(i))
            valor_combobox = valor_combobox.split('-')[0].strip()
            if valor_combobox == filial:
                self.driver.execute_script('document.getElementById("form:comboBoxLoja_items").children[{}].click()'.format(i))
                return
        raise ValueError('Filial nao encontrada')


    def __parse_csv_vendas(self, filepath:str=None) -> list:        
        
        list_vendas = []
        path = self.config['firefox']['default_download_path']
        for filename in os.listdir(path): filepath = '{}\\{}'.format(path, filename)    
        with open(filepath) as csv_file:
            csv_reader = csv.reader(csv_file, delimiter=',')
            line_count = 0
            for row in csv_reader:
                if line_count > 0:
                    list_vendas.append([row[0], row[1], row[2], row[3], row[4], row[5], row[6].strip(), 
                                    row[7], row[8], row[9], row[10], row[11], row[12], row[13], row[14], row[15], row[16], row[17]])            
                line_count += 1
        return list_vendas


    

    def __format_filial(self, filial:int) -> str:

        if filial > 0 and filial < 10:
            _filial_format = '000{}'.format(filial)
        elif filial >= 10 and filial < 100:
            _filial_format = '00{}'.format(filial)
        elif filial >= 100 and filial < 1000:
            _filial_format = '0{}'.format(filial)
        elif filial >= 1000:
            _filial_format = '{}'.format(filial)

        return _filial_format
    
    #TODO: Melhorar esse metodo.
    def __check_dados(self, max_t:int=5) -> None:
        _max_t = 0
        # Verificando a quantidade de dados renderizados na tela
        qtd_dados = self.driver.execute_script('return document.getElementById("form:idTabView:datatable_transacoes_data").children.length')        
        while qtd_dados == 1 and _max_t != max_t:                        
            qtd_dados = self.driver.execute_script('return document.getElementById("form:idTabView:datatable_transacoes_data").children.length')                                    
            time.sleep(1)
            _max_t += 1
        texto = self.driver.execute_script('return document.getElementById("form:idTabView:datatable_transacoes_data").children[0].innerText')
        return False if (qtd_dados == 1 and 'sem registros' in texto.lower())  else True