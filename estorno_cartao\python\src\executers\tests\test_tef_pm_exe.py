import sys
from pathlib import Path
from dataclasses import asdict


def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 3)

from executers.main_tef import TEF
from services.telegram_bot_service import *
from services.logger import create_logger
import utils
from Connections.search_db import SearchDb
from models.refund_log import estorno_venda_log_orm, turn_estornolog_into_legacy_dict



#POINT OF SALE
################################
# Argumento 0: pos pmenos      #
# Argumento 1: tef pmenos      #
# Argumento 2: retorno Cielo   #
# Argumento 3: retorno Getnet  #
# Argumento 4: tef Extrafarma  #
# Argumento 5: pos Extrafarma  #
# Argumento 6: Atl.Token Cielo #
# Argumento 7: Retorno Nupay   #
# Argumento 8: Retorno Itau    #
# Argumento 9: Verificar tickets zendesk #
################################

main_logger = create_logger('', "logs.log")
main_logger.setLevel(20)

if __name__ == '__main__':  

    if len(sys.argv) > 1:
        option = int(sys.argv[1]) 
    else:
        option = 1

    app_config = utils.get_config()  


    ids_estorno = (351917,) 

    requested_refund = []
    finder = SearchDb()

    for index, id_estorno in enumerate(ids_estorno):

        refund_info_raw = finder.search_refund_by_refund_id(id_estorno)
        refund_info = estorno_venda_log_orm(refund_info_raw[0][0])

        refund_info.idDevtrocaCab = int(refund_info.idDevtrocaCab)
        refund_info_legacy = turn_estornolog_into_legacy_dict(refund_info)

        requested_refund.append(refund_info_legacy)

    try:
        TEF(app_config, option).execute_process(requested_refund)
    

    # Tratando exessões do processo
    except Exception as e:

        if option == 0:
            msg = 'ESTORNO VENDA - ERRO POS:\n{}'.format('error {} - desc: {}'.format(e.__class__, e))

            if (msg != "ESTORNO VENDA - ERRO POS:\nerror <class 'ValueError'> - desc: Erro na requisicao: status 404"):

                telegram_bot_sendtext(msg)
        
        if option == 1:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO TEF:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 2:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 3:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO GET:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 4:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO TEF extrafarma:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 5:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO POS extrafarma:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 6:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO AO ATUALIZAR TOKEN:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
        
        if option == 7:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO NUPAY:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
           
        if option == 8:
            telegram_bot_sendtext('ESTORNO VENDA - ERRO RETORNO ITAU:\n{}'.format('error {} - desc: {}'.format(e.__class__, e)))
            
        if option != 1:
            msg = 'ESTORNO VENDA - ERRO TEF:\n{}'.format('error {} - desc: {}'.format(e.__class__, e))

            if (msg != "ESTORNO VENDA - ERRO TEF:\nerror <class 'ValueError'> - desc: Erro na requisicao: status 404"):

                telegram_bot_sendtext(msg)
            

         
        raise e   
    