from services.mercado_pago_services import ServiceMercadoPago
from services.movimento_service import MovimentoService
from services.tracking_services import TrackingServices
from services.planoAssinatura import PlanoAssinatura
from services.estorno_service import EstornoService
from services.vtex_api_service import VTEXHandler
from services.vtex.vtex_validations import VtexValidator
from services.getnet_service import ServiceGetnet
from services.cielo_service import ServiceCielo
from services.email_service import EmailService
from services.telegram_bot_service import *
from services.nupay.nupay_services import ServiceNuPay
from services.lio_service import LioService
from Connections.search_db import SearchDb
from services.vindi_services import Vin<PERSON>
from services.pagaleve.paga_leve_service import process_pagaleve_refund
from services.multi_payment_method_sale.multi_payment_method_service import get_card_refund_info_pos
from datetime import datetime
from services.sms import SMS  
import pandas as pd
import utils
from services.logger import create_logger
from typing import Dict, List
import Config.vars as  vars
from Config.vars import PAGUE_MENOS_CASE, CIELO_ACQUIRE_NAME, GETNET_ACQUIRE_NAME, ResultHandle, STATUS_WAIT_CIELO, STATUS_WAIT_GETNET,STATUS_PENDENCY_FOR_CSC
from aux_temp import register_refund_activity
from models.card_refund_info import CardRefundInfo


if __name__ == '__main__': 
    pg_non_tef_logger = create_logger('')
    pg_non_tef_logger.setLevel(10)
else: 
    pg_non_tef_logger = create_logger(__name__, without_handler = True)



class POS():

    def __init__(self, config, path_download):       
        
        self.mercadopago_services = ServiceMercadoPago(config)
        self.movimento_service = MovimentoService(config)
        self.tracking_services = TrackingServices(config)
        self.estorno_service = EstornoService(config)
        self.getnet_services = ServiceGetnet(config)
        self.service_cielo = ServiceCielo(config)
        self.lio_sevices = LioService(config)
        self.path_download = path_download
        self.sms_sevices = SMS(config)  
        self.search = SearchDb()          
        self.config = config
        self.vindi = Vindi()
        self.ids_estorno_to_skip = []
        
    
        super().__init__()
   

    # Retorna um conjunto de dados que sera usado para gerar as planilhas enviadas aos adquirentes
    def execute_process(self, requested_refund: List[Dict] = None) -> None:
        print("POS - execute_process()")

        vtex_service = VTEXHandler(self.config['api']['vtex'])

        auth_token = PlanoAssinatura.auth_token()
        
        telegram_bot_sendtext("#####################")
        telegram_bot_sendtext("Iniciando Estorno POS")
        telegram_bot_sendtext("#####################")

        pg_non_tef_logger.info("Iniciando Estorno de venda não TEF")
        
        registro_nome_arquivo = self.__gerar_nome_arquivo()
        self.estorno_service.gerar_linha_cabecalho(registro_nome_arquivo)

        if requested_refund is None:
            pg_non_tef_logger.debug("Processando estornos de venda não TEF pendentes")
            movimento = self.movimento_service.get_vendas_ecommerce()
        else:
            pg_non_tef_logger.info("Processando estornos de venda não TEF selecionados")
            movimento = requested_refund

        for venda in movimento:
            result = self.search.search_duplicity_mov(venda['idTrocaCab'])
            self.ids_estorno_to_skip.extend(result)
            if venda['idEstorno'] in self.ids_estorno_to_skip: continue
        

            if venda['numeroPedidoVTEX'] != '' and venda['numeroPedidoVTEX'] != None:
                venda = self.__autalizar_data_venda(venda)

            filial_origem = venda['codigoFilialOrigem']
            cnpj_filial_origem = venda['cnpjFilialOrigem']    
            filial_destino = venda['codigoFilialDestino']
            cnpj_filial_destino = venda['cnpjFilialDestino']
            numero_cupom = int(venda['numeroCupom'])
            numero_coo = venda['numeroCoo']
            pdv = venda['pdvDevolucao']
            nome_cliente = venda['nomeCliente']
            numero_pedido_vtex = venda['numeroPedidoVTEX']
            numero_pedido_delivery = venda['numeroPedidoDelivery']
            data_venda_movimento = venda['dataMovimento']
            valor_venda_movimento = float(venda['valorVenda'])
            valor_estorno = float(venda['valorEstorno'])
            data_devolucao = venda['dataDevolucao'] if venda['dataDevolucao'] != '0001-01-01T00:00:00' else None
            nsu_movimento_tef = venda['codigoNSUTEF']
            nsu_movimento_host = venda['codigoNSUHOST']
            bandeira_cartao_movimento = venda['bandeira']
            telefone = venda['telefoneCliente']
            email = venda['emailCliente']
            numero_pre_venda = venda['numeroPreVenda']
            plano_assinatura = venda['planoAssinatura']
            canal_venda = venda['canalVendas']
            cartao_sempre = venda['cartaoSempre']
            parcelas = venda['quantParcelas']
            tipo_venda = venda['tipo']
            flag_pag_uni = venda['flagPagamentoUnificado']
            nsu_pos = venda['nsuPos']
            id_troca_cab = str(venda['idTrocaCab'])
            coo_pag_uni = venda['cooPagUni']
            id_estorno = venda['idEstorno']
            new_data = data_venda_movimento.split('/')

            if id_estorno in (351401, 351595,): continue

            nome_empresa = "Pague Menos"
            if filial_origem >= 7000 and filial_origem <8000:
                nome_empresa = "Extrafarma"
                print('venda Extrafarma')
                continue

            if utils.avoid_legacy_api_duplicates(id_estorno) and id_estorno not in (412358,): 
                pg_non_tef_logger.info(f"Pedido de estorno {id_estorno} ja foi processado")
                continue
            
            obj_estorno_log = {
                "codigoFilialOrigem": filial_origem,
                "cnpjFilialOrigem": cnpj_filial_origem,
                "codigoFilialDestino": filial_destino,
                "cnpjFilialDestino": cnpj_filial_destino,
                "numeroCupom": numero_cupom,
                "numeroCoo": numero_coo,
                "nomeCliente": nome_cliente,
                "pdvInformado": pdv,
                "numeroPedidoVTEX": numero_pedido_vtex,
                "numeroPedidoDelivery": numero_pedido_delivery,
                "dataMovimento": '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0]),
                "valorTotalVenda": valor_venda_movimento,
                "valorEstorno": valor_estorno,
                "dataDevolucao":data_devolucao,
                "nsutef": nsu_movimento_tef,
                "nsuhost": nsu_movimento_host,
                "bandeira": bandeira_cartao_movimento,
                "telefone": telefone,
                "email": email,
                "numeroPreVenda": numero_pre_venda,
                "planoAssinatura": plano_assinatura,
                "canalVendas": canal_venda,                
                "cartaoSempre": cartao_sempre,
                "parcelas": parcelas,
                "tipo": tipo_venda,
                "flagPagamentoUni": flag_pag_uni,
                "nsuPos": nsu_pos,
                "idDevtrocaCab": id_troca_cab,
                "cooPagUni": coo_pag_uni,
                "idEstorno": id_estorno,
                "lote":self.__montar_lote('{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])),                              
                "tipoEstorno":"Estorno Ecommerce",
                "nomeEmpresa":nome_empresa
            }
            
            if utils.is_to_avoid_brand(bandeira_cartao_movimento): 
                pg_non_tef_logger.info(f"Bandeira {bandeira_cartao_movimento} nao sera processada")
                continue

            tipo_pagamento = utils.is_a_mapped_payment_method(bandeira_cartao_movimento, 
                                                                self.estorno_service, 
                                                                obj_estorno_log)
            
            if tipo_pagamento == -1: continue

            
            if tipo_venda == '' or tipo_venda == None:
                obj_estorno_log['motivo'] = 'Venda PDV Antigo estonanda no troca e devolução'
                obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END               
                self.estorno_service.record_refund_processment(obj_estorno_log)
                telegram_bot_sendtext("Estorno {}:Venda PDV Antigo estonanda no troca e devolução".format(id_estorno))
                continue

            if plano_assinatura == 'S':
                
                retorno = PlanoAssinatura.check_assinatura(numero_pre_venda,auth_token)
                
                if retorno[2:9] == 'sucesso':
                     print('...Assinatura Cancelada com Sucesso...')
                     obj_estorno_log['motivo'] = 'Assinatura Cancelada' 
                     obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END   
                     self.estorno_service.record_refund_processment(obj_estorno_log)
                     telegram_bot_sendtext("Estorno {}:Assinatura Cancelada".format(id_estorno))
                     continue
                     

                else:
                     print('...Venda nao localizada na SCOPE...')
                     obj_estorno_log['motivo'] = 'Não foi possivel Cancelar a Assinatura' 
                     obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC   
                     self.estorno_service.record_refund_processment(obj_estorno_log)
                     telegram_bot_sendtext("Estorno {}:Não foi possivel Cancelar a Assinatura".format(id_estorno))
                     continue
            
            
            if tipo_pagamento == 'pix':
                
                if obj_estorno_log['bandeira'] == 'PIX VINDI':

                    msg_vindi, code_vindi = self.vindi.cancellation(filial=filial_origem,
                                                                    pre_sale=numero_pre_venda,
                                                                    refund_value=obj_estorno_log["valorEstorno"])

                    if code_vindi in (-1, -2):

                        obj_estorno_log['motivo'] = msg_vindi[0:254]
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC                       
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Venda Pix Não localizada na Veloce POS".format(id_estorno))
                        continue

                    elif code_vindi in (-10, -20):

                        obj_estorno_log['motivo'] = msg_vindi[0:254]
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC                       
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Venda Pix vindi erro na API".format(id_estorno))
                        continue
                    elif code_vindi in [-3]:

                        obj_estorno_log['motivo'] = msg_vindi
                        obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END 
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Estorno ja consta como REALIZADO".format(id_estorno))
                        continue

                    else:

                        obj_estorno_log['motivo'] = 'REALIZADO'
                        obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Compra cancelada".format(id_estorno))
                        continue


                
                
                #VELOCE
                if numero_pedido_vtex == None or numero_pedido_vtex == "":
                    
                    token_lio = self.lio_sevices.get_token() 
                    cnpjformatado = self.tratarnum(cnpj_filial_origem)
                    dados_veloce = self.lio_sevices.consultar_vendas(data_venda_movimento,cnpjformatado[1:],numero_cupom,token_lio)
                    
                    if dados_veloce == -1:
                        obj_estorno_log['motivo'] = 'Venda Pix Não localizada na Veloce POS'
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC                       
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Venda Pix Não localizada na Veloce POS".format(id_estorno))
                        continue

                    
                    tipo = 'tef_pgm'
                    
                    obj_estorno_log['nsutef'] = nsu = dados_veloce['pagamentos'][0][0]['order_id']
                    obj_estorno_log['nsuhost'] = nsu = dados_veloce['pagamentos'][0][0]['order_id']

                #VTEX
                else:

                    vtex_referee  = VtexValidator(obj_estorno_log)
                    vtex_sale_info, obj_estorno_log = vtex_referee.get_vtex_info()

                    if vtex_sale_info == -1:
                        register_refund_activity(obj_estorno_log, self.estorno_service) 
                        continue
                
                    tipo = 'pos_pgm'
                    obj_estorno_log['nsutef'] = vtex_sale_info['nsu']
                    obj_estorno_log['nsuhost'] = vtex_sale_info['nsu']
                    
                    
                    if obj_estorno_log['bandeira'].lower() == utils.PIX_PAGA_LEVE_LABEL:

                        _, refund_info = process_pagaleve_refund(vtex_sale_info["nsu"], obj_estorno_log)
                        register_refund_activity(refund_info, self.estorno_service) 
                        continue


                obj_estorno_log['bandeira'] = 'Pix'

                estorno = self.mercadopago_services.estornopix(obj_estorno_log['nsutef'], valor_venda_movimento, valor_estorno, tipo)
                
                
                if estorno == -1:
    
                    obj_estorno_log['motivo'] = 'codigo nsu invalido no Mercado Pago' 
                    obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    telegram_bot_sendtext("Estorno {}: codigo nsu invalido no Mercado Pago".format(id_estorno))
                    continue
                    
                elif estorno == -2:
                    
                    obj_estorno_log['motivo'] = 'Estorno ja consta como REALIZADO' 
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END 
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    telegram_bot_sendtext("Estorno {}:Estorno ja consta como REALIZADO".format(id_estorno))
                    continue

                elif estorno == -20:
                    error_description = "venda desse estorno consta como rejeitada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END 
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    telegram_bot_sendtext(msg)
                    pg_non_tef_logger.info(msg)
                    continue
                    
                elif estorno == -3:
                    
                    obj_estorno_log['motivo'] = 'Estorno ja consta como Parcialmete REALIZADO' 
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    telegram_bot_sendtext("Estorno {}:Estorno ja consta como Parcialmete REALIZADO".format(id_estorno))
                    continue
                    
                elif estorno == -30:
                    error_description = "status de pix mercado pago não mapeada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    telegram_bot_sendtext(msg)
                    pg_non_tef_logger.info(msg)
                    continue

                elif estorno == -4:
                    
                    obj_estorno_log['motivo'] = 'Valor do estonar Maior que o valor total da Venda' 
                    obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    telegram_bot_sendtext("Estorno {}:Valor do estonar Maior que o valor total da Venda".format(id_estorno))
                    continue

                elif estorno == -5:

                    obj_estorno_log['motivo'] = 'REALIZADO'
                    obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END
                    self.estorno_service.record_refund_processment(obj_estorno_log)
                    self.sms_sevices.sendsms(telefone,cartao_sempre,valor_estorno)
                    telegram_bot_sendtext("Estorno {}:REALIZADO".format(id_estorno))
                    continue

                elif estorno == -6:
                    obj_estorno_log['motivo'] = 'Venda se encontra com Status de Cancelada na Mercado Pago'
                    obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)                   
                    telegram_bot_sendtext("Estorno {}:Venda se encontra com Status de Cancelada na Mercado Pago".format(id_estorno))
                    continue

                elif estorno == -11:
                    obj_estorno_log['motivo'] = 'Venda Pix passou do prazo de 60 dias.'
                    obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)                   
                    telegram_bot_sendtext("Estorno {}:Venda Pix passou do prazo de 60 dias.".format(id_estorno))
                    continue

                else:
                    error_description = "resposta de procesamento de estorno pix mercado pago não mapeada"
                    msg = f"{vars.BASE_MSG.format(id_estorno=id_estorno)}{error_description}"
                    obj_estorno_log['motivo'] = error_description
                    obj_estorno_log['status'] = vars.STATUS_PENDENCY_FOR_CSC
                    self.estorno_service.record_refund_processment(obj_estorno_log)                   
                    telegram_bot_sendtext(msg)
                    pg_non_tef_logger.info(msg)
                    continue


            elif tipo_pagamento == 'cartao':

                today = datetime.today()          
                datastr = '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])         
                data = pd.to_datetime(datastr) + pd.DateOffset(hours = 32)     
                
                if (data >= today):
                    print('Venda lançada no mesmo dia')              
                    continue
                
                # VELOCE Cartão
                if numero_pedido_vtex == None or numero_pedido_vtex == "":
                    

                    print("veloce_pos")
                   
                    cnpjformatado = self.tratarnum(cnpj_filial_origem)
                    token_lio = self.lio_sevices.get_token() 
                    dados_veloce = self.lio_sevices.consultar_vendas(data_venda_movimento,cnpjformatado[1:],numero_cupom,token_lio)
                    
                    if dados_veloce == -1:
                        print('...Venda nao localizada na VELOCE...')
                        obj_estorno_log['motivo'] = 'Venda nao localizada na veloce' 
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC   
                        self.estorno_service.record_refund_processment(obj_estorno_log) 
                        telegram_bot_sendtext("Estorno {}:Venda nao localizada na veloce".format(id_estorno))
                        continue

                    veloce_general_info = dados_veloce['pagamentos'][0][0]


                    if veloce_general_info == None or veloce_general_info == "":
                        print('...Venda não possui dados de Pagamento...')
                        obj_estorno_log['motivo'] = 'Venda sem dados de Pagamento' 
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC   
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Venda sem dados de Pagamento".format(id_estorno)) 
                        continue

                    if veloce_general_info['dt_pagamento'] is None:
                        msg = "Sem data de pagamento na Veloce"
                        print(msg)
                        obj_estorno_log['motivo'] = msg 
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC   
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Venda sem dados de Pagamento".format(id_estorno)) 
                        continue
  

                    dt_pagamento = datetime.strptime(veloce_general_info['dt_pagamento'],'%Y-%m-%d %H:%M:%S')
                    objPag = {
                        'quantidade_parcelas':veloce_general_info['parcelas'],
                        'data_venda':dt_pagamento.strftime('%Y/%m/%d')
                    }
                    print(objPag['data_venda'])
    


                    if obj_estorno_log['nsuhost'].isnumeric() == False:
                        obj_estorno_log['motivo'] = 'Numero NSU Invalido'
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Codigo do estabelecimento nao identificado".format(id_estorno))
                        #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_log['motivo'])
                        continue

                    try: 
                       obj_estorno_log = utils.verify_diff_values(obj_estorno_log, veloce_general_info["valor"])
                    except utils.DiffValueError:
                        obj_estorno_log['motivo'] = 'Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)'
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)".format(id_estorno))
                        continue

                    card_requesition_info, refund_info = get_card_refund_info_pos(obj_estorno_log, veloce_general_info)
                    obj_estorno_log["codigoAutorizacao"] = refund_info.codigoAutorizacao
                    obj_estorno_log["nsuhost"] = refund_info.nsuhost
                    obj_estorno_log["nsutef"] = refund_info.nsutef

                    if isinstance(card_requesition_info, ResultHandle):
                        register_refund_activity(refund_info, self.estorno_service) 
                        continue
                    
                    

                    if veloce_general_info['adquirente'].lower() == CIELO_ACQUIRE_NAME.lower(): 

                        protocolo = self.service_cielo.estornoCielo(card_requesition_info, PAGUE_MENOS_CASE)
                    
                        if protocolo == -1: 

                            msg = "Erro ao pedir estorno na adquirente cielo"
                            obj_estorno_log['motivo'] = msg
                            obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC    
                            self.estorno_service.record_refund_processment(obj_estorno_log)
                            telegram_bot_sendtext("Estorno {}: Erro ao pedir estorno na adquirente cielo".format(id_estorno))
                            continue

                        elif protocolo == 2:
                            continue

                        obj_estorno_log['protocoloCielo'] = protocolo
                        obj_estorno_log['nomeArquivo'] = registro_nome_arquivo
                        obj_estorno_log['status'] = STATUS_WAIT_CIELO
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        continue
                   
                    
                    if veloce_general_info['adquirente'].lower() == GETNET_ACQUIRE_NAME.lower():


                        protocolo = self.getnet_services.cancel_getnet_service(card_requesition_info, PAGUE_MENOS_CASE)

                        if protocolo == -1: 
                            msg = "Erro ao pedir estorno na adquirente getnet"
                            obj_estorno_log['motivo'] = msg
                            obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC                                            
                            self.estorno_service.record_refund_processment(obj_estorno_log)
                            telegram_bot_sendtext("Estorno {}:Venda não localizada para estorno".format(id_estorno))
                            continue
                        
                        obj_estorno_log['protocoloGetnet'] = protocolo
                        obj_estorno_log['nomeArquivo'] = registro_nome_arquivo
                        obj_estorno_log['status'] = STATUS_WAIT_GETNET
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        continue        
                    
                else:
                
                    dados_vtex = vtex_service.retrieve_data(obj_estorno_log)                            

                    if dados_vtex == -1:
                        obj_estorno_log['motivo'] = 'Codigo da VTEX invalido' 
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Codigo da VTEX invalido".format(id_estorno))  
                        #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])               
                        continue
                    elif dados_vtex == -2:
                        obj_estorno_log['motivo'] = 'Códigos de autorização não encontrados na VTEX'
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}: Códigos de autorização não encontrados na VTEX".format(id_estorno))
                        #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])
                        continue
                    elif dados_vtex == -3:
                        obj_estorno_log['motivo'] = 'Compra cancelada'
                        obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Compra cancelada".format(id_estorno))
                        #self.tracking_services.post_tracking(venda, vars.STATUS_REFUND_PROCESS_END, vars.STATUS_REFUND_PROCESS_END, content=obj_estorno_det['motivo'])
                        continue
                    elif dados_vtex == -4:
                        obj_estorno_log['motivo'] = 'Compra feita a dinheiro'
                        obj_estorno_log['status'] = vars.STATUS_REFUND_PROCESS_END    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Compra feita a dinheiro".format(id_estorno))
                        #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])
                        continue
                    elif dados_vtex == -8:
                        obj_estorno_log['motivo'] = 'Nao se trata de uma venda Pague Menos'
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Compra feita a dinheiro".format(id_estorno))
                        #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])
                        continue
                   
                    try: 
                       obj_estorno_log = utils.verify_diff_values(obj_estorno_log, dados_vtex['valor'])
                    except utils.DiffValueError:
                        obj_estorno_log['motivo'] = 'Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)'
                        obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC    
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        telegram_bot_sendtext("Estorno {}:Divergencia de valores entre Troca devolução e outras fontes (VTEX ou VDA)".format(id_estorno))
                        #self.tracking_services.post_tracking(venda, 'ANALISE', 'Em analise', content=obj_estorno_det['motivo'])
                        continue


                    obj_estorno_log['dataVenda'] = '{}-{}-{}'.format(new_data[2],new_data[1],new_data[0])
                    obj_estorno_log['n_cartao'] = dados_vtex['n_cartao']
                    obj_estorno_log['sequencial_controle'] = dados_vtex['sequencial_controle']
                    obj_estorno_log['tid'] = dados_vtex['t_id']
                    obj_estorno_log['codigoAutorizacao'] = dados_vtex['codigo_autorizacao']                     
                    obj_estorno_log['nsuhost'] = dados_vtex['nsu_settle'] if bool(dados_vtex['nsu_settle']) else dados_vtex['nsu'] 
                    
                    pg_non_tef_logger.info(f"Adquirente: {dados_vtex['adquirente']}")
                    
                    
                    if dados_vtex['adquirente'].lower() == CIELO_ACQUIRE_NAME.lower():

                        card_requesition_info = CardRefundInfo(nsu_tef=obj_estorno_log['nsuhost'],
                                            nsu_external = obj_estorno_log['nsuhost'],
                                            establishment_code=dados_vtex["cod_estabelecimento"],
                                            acquirer_code=102,
                                            installments=obj_estorno_log['parcelas'],
                                            auth_code=obj_estorno_log['codigoAutorizacao'],
                                            payment_method_value=dados_vtex['valor'],
                                            refund_value=obj_estorno_log["valorEstorno"],
                                            sale_date=obj_estorno_log['dataVenda'])

                        protocolo = self.service_cielo.estornoCielo(card_requesition_info, PAGUE_MENOS_CASE)
                        
                        if protocolo == -1: 

                            msg = "Erro ao pedir estorno na adquirente cielo"
                            obj_estorno_log['motivo'] = msg
                            obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC    
                            self.estorno_service.record_refund_processment(obj_estorno_log)
                            telegram_bot_sendtext("Estorno {}: Erro ao pedir estorno na adquirente cielo".format(id_estorno))
                            continue

                        elif protocolo == 2:
                            continue

                        obj_estorno_log['protocoloCielo'] = protocolo
                        obj_estorno_log['nomeArquivo'] = registro_nome_arquivo
                        obj_estorno_log['status'] = STATUS_WAIT_CIELO
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        continue
                        
                    if dados_vtex['adquirente'].lower() == GETNET_ACQUIRE_NAME.lower():
                            
                        card_requesition_info = CardRefundInfo(nsu_tef=obj_estorno_log['nsuhost'],
                                        nsu_external = obj_estorno_log['nsuhost'],
                                        establishment_code=dados_vtex["cod_estabelecimento"],
                                        acquirer_code=108,
                                        installments=obj_estorno_log['parcelas'],
                                        auth_code=obj_estorno_log['codigoAutorizacao'],
                                        payment_method_value=dados_vtex['valor'],
                                        refund_value=obj_estorno_log["valorEstorno"],
                                        sale_date=obj_estorno_log['dataVenda'])
                

                        protocolo = self.getnet_services.cancel_getnet_service(card_requesition_info, PAGUE_MENOS_CASE)
                        if protocolo == -1: 
                            continue

                        obj_estorno_log['protocoloGetnet'] = protocolo
                        obj_estorno_log['nomeArquivo'] = registro_nome_arquivo
                        obj_estorno_log['status'] = STATUS_WAIT_GETNET
                        self.estorno_service.record_refund_processment(obj_estorno_log)
                        continue
                        
            elif tipo_pagamento == 'hibrido':
                
                
                vtex_referee  = VtexValidator(obj_estorno_log)
                vtex_sale_info, obj_estorno_log = vtex_referee.get_vtex_info()

                if vtex_sale_info == -1:
                    register_refund_activity(obj_estorno_log, self.estorno_service) 
                    continue

                nupay = ServiceNuPay(self.config, "pm")
                refund_request, obj_estorno_log = nupay.refund_sale(vtex_sale_info["t_id"], obj_estorno_log)


                obj_estorno_log['nomeArquivo'] = registro_nome_arquivo
                register_refund_activity(obj_estorno_log, self.estorno_service) 
                continue

            else:
                obj_estorno_log['motivo'] = 'Erro não mapeado no RPA'
                obj_estorno_log['status'] = STATUS_PENDENCY_FOR_CSC
                self.estorno_service.record_refund_processment(obj_estorno_log)
                telegram_bot_sendtext("URGENTE: Erro não mapeado no RPA") 
                continue  
        
        telegram_bot_sendtext("Estorno Pos Concluido")    
            

    def __autalizar_data_venda(self, dados_venda:dict) -> dict:        
        vtex_service = VTEXHandler(self.config['api']['vtex'])
        data = vtex_service.retrieve_creation_date(dados_venda['numeroPedidoVTEX'])
        if data == -1: return dados_venda
        dados_venda['dataMovimentoVtex'] = data
        return dados_venda      

    def __gerar_nome_arquivo(self) -> str:              
        id_arq = datetime.today().strftime('%d/%m/%Y/%H/%M').replace('/', '')
        id_arq += 'POSV3'
        return id_arq


    def __montar_lote(self, data:str) -> str:
        _data_aux = data.split('-')
        lote = datetime.strftime(datetime.strptime('{}/{}/{}'.format(_data_aux[2], _data_aux[1], _data_aux[0]), 
                                                    '%d/%m/%Y'), '%y%m%d')
        return lote        

    def tratarnum(self,numVtex):        
        numero = ""
        for x in numVtex:
            if x.isdigit():
                numero = numero +""+ x
        return numero      
    
    def verifica_horaio_cielo(self):
        hora = datetime.now().time()

        inicio_periodo = datetime(2023,6,7, 18, 0, 0).time()
        fim_periodo = datetime(2023,6,7, 20, 0, 0).time()

        if inicio_periodo <= hora <= fim_periodo:
            return -1
        else:
            return 0