from typing import List
from dataclasses import dataclass
import Config.vars as vars
import locale
from services.zendesk.api_requests import get_field_info
from models.refund_log import EstornoLog
from utils import enterprise_code_of_the_branch
from services.zendesk.zen_tools import ensure_search, format_currency_value
from services.logger import create_logger
import services.zendesk.references.map_ids as map_ids
from services.multi_payment_method_sale.multi_payment_method_service import get_sale_payment_methods
from Config.vars import ResultHandle
from re import search


if __name__ == '__main__': 
    zen_cf_logger = create_logger('')
    zen_cf_logger.setLevel(10)
else: 
    zen_cf_logger = create_logger(__name__, without_handler = True)

@dataclass
class FieldInfo:
    id: int
    name: str
    value: str

class BaseCustomFieldsCreator:
    def __init__(self, env: str = "prd") -> None:
        self.env = env
        self.fields: List[FieldInfo] = []
        self.refund_info: EstornoLog = None

    def _add_field(self, field: FieldInfo) -> None:

        self.fields.append({
            "id": field.id,
            "value": field.value
        })
    
    def _get_field_id(self, field_name: str, kind: str = "common")-> int:
        """Obter o ID do campo de acordo com o ambiente que a 
        aplicação está executando

        Args:
            field_name (str): Nome do campo
            kind (str): Tipo de campo. Pode ser `pix`, `card` e `common`

        Returns:
            int: ID do campo
        """
        if kind == "common":
            return map_ids.COMMON_FIELD_ID_MAPPING[self.env].get(field_name)
        elif kind == "card":
            return map_ids.FIELD_ID_MAPPING_FOR_CARD[self.env].get(field_name)
        elif kind == "pix":
             return map_ids.FIELD_ID_MAPPING_FOR_PIX[self.env].get(field_name)
        else:
            raise ValueError(f"Tipo de campo |{kind}| não está mapeado")

    def _add_ended_sale(self):
        id = self._get_field_id("venda_finalizada")
        self._add_field(FieldInfo(id, "Venda Finalizada", "venda_finalizada_sim"))

    def _add_manager_attachment(self):
        id = self._get_field_id("anexo_autorizacao_gestor")
        self._add_field(FieldInfo(id, "Anexo do Gestor", False))

    def _add_direct_manager_attachment(self):
        id = self._get_field_id("anexo_autorizacao_gestor_imediato")
        self._add_field(FieldInfo(id, "Anexo do Gestor Direto", False))

    def _add_bill(self):
        id = self._get_field_id("anexo_cupom_fiscal")
        self._add_field(FieldInfo(id, "Nota Fiscal", False))

    def _add_approval_flow(self):
        id = self._get_field_id("anexo_fluxo_aprovacao")
        self._add_field(FieldInfo(id, "Fluxo de Aprovação", False))

    def _add_proof_of_purchase(self):
        id = self._get_field_id("anexo_comprovante_compra")
        self._add_field(FieldInfo(id, "Comprovante de Compra", False))

    def _add_authoriztion_of_package_manager(self):
        id = self._get_field_id("anexo_autorizacao_gestor_pacote")
        self._add_field(FieldInfo(id, "Autorização do Gestor do Pacote", False))

    def _add_attachment_photo(self):
        id = self._get_field_id("anexo_foto")
        self._add_field(FieldInfo(id, "Anexo Foto", False))

    def _add_attachment_invoice_or_guide(self):
        id = self._get_field_id("anexo_fatura_guia")
        self._add_field(FieldInfo(id, "Anexo Nota Fiscal ou Guia", False))

    def _add_attachment_receipts(self):
        id = self._get_field_id("anexo_comprovantes")
        self._add_field(FieldInfo(id, "Anexo Recibos", "anexo_recibos_nao_se_aplica"))

    def _add_attachment_package_manager_authorization(self):
        id = self._get_field_id("anexo_autorizacao_gestor_pacote")
        self._add_field(FieldInfo(id, "Anexo Autorização Gestor do Pacote", False))

    def _add_attachment_invoice(self):
        id = self._get_field_id("anexo_nota_fiscal")
        self._add_field(FieldInfo(id, "Anexo Nota Fiscal", False))

    def _add_attachment_photo_or_minimum_weight_evidence(self):
        id = self._get_field_id("anexo_foto_peso_minimo")
        self._add_field(FieldInfo(id, "Anexo Foto ou Evidência de Peso Mínimo", False))

    def _add_attachment_fuel_card(self):
        id = self._get_field_id("anexo_cartao_combustivel")
        self._add_field(FieldInfo(id, "Anexo Cartão Combustível", False))

    def _add_attachment_driver_license(self):
        id = self._get_field_id("anexo_cnh")
        self._add_field(FieldInfo(id, "Anexo CNH", False))

    def _add_unproductive_connection(self):
        id = self._get_field_id("ligacao_improdutiva")
        self._add_field(FieldInfo(id, "Conexão Improdutiva", False))

    def _add_attachment_corporate_card(self):
        id = self._get_field_id("anexo_cartao_corporativo")
        self._add_field(FieldInfo(id, "Anexo Cartão Corporativo", False))

    def _add_attachment_return_receipt(self):
        id = self._get_field_id("anexo_comprovante_devolucao")
        self._add_field(FieldInfo(id, "Anexo Comprovante de Devolução", False))

    def _add_attachment_fiscal_receipt(self):
        id = self._get_field_id("anexo_cupom_fiscal")
        self._add_field(FieldInfo(id, "Anexo Cupom Fiscal", False))

    def _add_offers(self, value: str):
        id = self._get_field_id("ofertas")
        self._add_field(FieldInfo(id, "Ofertas", value))

    def _add_attachment_pos_receipt_via_store(self):

        id = self._get_field_id("anexo_comprovante_pos_via_loja")

        if (self.refund_info.tipo == "S" and (self.refund_info.numeroPedidoVTEX is None or self.refund_info.numeroPedidoVTEX == "")):
            self._add_field(FieldInfo(id, "Anexo Comprovante POS via Loja", True))
        else:
            self._add_field(FieldInfo(id, "Anexo Comprovante POS via Loja", False))
        

    def _add_attachment_key_delivery_term(self):
        id = self._get_field_id("anexo_termo_entrega_chaves")
        self._add_field(FieldInfo(id, "Anexo Termo de Entrega de Chave", False))

    def _add_attachment_receipt_for_change(self):
        id = self._get_field_id("anexo_comprovante_devolucao")
        self._add_field(FieldInfo(id, "Anexo Recibo para Troco", False))

    def _add_create_ticket_for_a_store(self):
        id = self._get_field_id("criar_ticket_loja")
        self._add_field(FieldInfo(id, "Criação de Ticket para uma Loja", False))

    def _add_create_ticket_for_a_internal_sector(self):
        id = self._get_field_id("criar_ticket_setor_interno")
        self._add_field(FieldInfo(id, "Criação de Ticket para um Setor Interno", False))

    def _add_update_agent(self, value: str):
        id = self._get_field_id("atualizador")
        self._add_field(FieldInfo(id, "Atualizador Agente", value))

    def _add_sale_date(self, value: str):
        id = self._get_field_id("data_venda")
        self._add_field(FieldInfo(id, "Data da Venda", value))
    
    def _add_enterprise(self):

        enterprise_code = enterprise_code_of_the_branch(self.refund_info.codigoFilialOrigem)

        if enterprise_code == vars.PAGUE_MENOS_CASE:
            value = "lojas_pague_menos"
        elif enterprise_code == vars.EXTRA_FARMA_CASE:
            value = "lojas_extrafarma"
        else:
            raise ValueError("Código de empresa năo mapeado")

        id = self._get_field_id("lojas")
        self._add_field(FieldInfo(id, "Lojas", value))
    
    def _add_branch_number(self):

        enterprise_code = enterprise_code_of_the_branch(self.refund_info.codigoFilialOrigem)

        if enterprise_code == vars.PAGUE_MENOS_CASE:
            id_field = self._get_field_id("numero_filial_pm")
        elif enterprise_code == vars.EXTRA_FARMA_CASE:
            id_field = self._get_field_id("numero_filial_exf")
        else:
            raise ValueError("Código de empresa năo mapeado")

        field_info = get_field_info(id_field, self.env)
        if field_info.failure:
            raise ValueError(f"Erro ao consultar. informaçőes do campo {id_field}")
        
        field_info = field_info.data

        index = ensure_search(field_info["ticket_field"]["custom_field_options"], 
                        str(self.refund_info.codigoFilialOrigem), 1)

        value = field_info["ticket_field"]["custom_field_options"][index]["value"]

        self._add_field(FieldInfo(id_field, "Num da Loja Pague Menos", value))
    
    def _add_refund_request_value(self):
        value = format_currency_value(self.refund_info.valorEstorno)
        id = self._get_field_id("valor_cancelamento")
        self._add_field(FieldInfo(id, "Valor do Cancelamento", value))

    def _add_sale_value(self):
        value = format_currency_value(self.refund_info.valorTotalVenda)
        id = self._get_field_id("valor_transacao")
        self._add_field(FieldInfo(id, "Valor da Transação", value))

    def _add_reason(self):
        id = self._get_field_id("motivo_estorno")
        self._add_field(FieldInfo(id, "Motivo do Estorno/Devolução", self.refund_info.motivo))
    
    def _add_cancel_type(self, value: str):
        id = self._get_field_id("cancelamento")
        self._add_field(FieldInfo(id, "Cancelamento", value))
    
    def _add_ecommerce_number(self):

        if (self.refund_info.tipo == "S" 
        and self.refund_info.numeroPedidoVTEX is not None 
        and len(self.refund_info.numeroPedidoVTEX)) > 10:

            value = self.refund_info.numeroPedidoVTEX

            id = self._get_field_id("numero_pedido_web")
            self._add_field(FieldInfo(id, "Número do E-commerce", value))
    

    def _add_id_devtroca_cab(self):

        id = self._get_field_id("numero_solicitacao_de_estorno")
        self._add_field(FieldInfo(id, "Id solicitação de estorno", self.refund_info.idDevtrocaCab))


    def _add_constant_fields(self):
        self._add_id_devtroca_cab()
        self._add_ended_sale()
        self._add_manager_attachment()
        self._add_direct_manager_attachment()
        self._add_bill()
        self._add_approval_flow()
        self._add_proof_of_purchase()
        self._add_authoriztion_of_package_manager()
        self._add_attachment_photo()
        self._add_attachment_invoice_or_guide()
        self._add_attachment_receipts()
        self._add_attachment_package_manager_authorization()
        self._add_attachment_invoice()
        self._add_attachment_photo_or_minimum_weight_evidence()
        self._add_attachment_fuel_card()
        self._add_attachment_driver_license()
        self._add_unproductive_connection()
        self._add_attachment_corporate_card()
        self._add_attachment_return_receipt()
        self._add_attachment_fiscal_receipt()
        self._add_offers("ptp_otc_cancelamento")
        self._add_attachment_pos_receipt_via_store()
        self._add_attachment_key_delivery_term()
        self._add_attachment_receipt_for_change()
        self._add_create_ticket_for_a_store()
        self._add_create_ticket_for_a_internal_sector()
        self._add_update_agent("atualizador_agente")

    def is_a_valid_nsu(self,  data: str, default_data: str = "99999"):

        validation = search(r"^[+-]?\d+$", data)

        if validation is None:
            return default_data
        else:
            return data


class PixCustomFieldsCreator(BaseCustomFieldsCreator):
    def __init__(self, refund_info: EstornoLog, env: str = "prd") -> None:
        super().__init__(env)
        self.refund_info = refund_info

    def create_list(self):
        self._add_kind_of_pix_sale()
        self._add_sale_date(self.refund_info.dataMovimento)
        self._add_customer_name()
        self._add_ecommerce_number()
        self._add_enterprise()
        self._add_cancel_type("cancelamento_cancelamento_venda_em_pix")
        self._add_nsu_tef()
        self._add_refund_request_value()
        self._add_branch_number()
        self._add_sale_value()
        self._add_reason()
        
        self._add_constant_fields()

    def _add_kind_of_pix_sale(self):
        if self.refund_info.tipo == "N":
            kind_of_pix_sale_value = "tipo_venda_pix_ecfpdv" 
        elif (self.refund_info.tipo == "S" and (self.refund_info.numeroPedidoVTEX is None or self.refund_info.numeroPedidoVTEX == "")):
            kind_of_pix_sale_value = "tipo_venda_pix_poslio"
        elif (self.refund_info.tipo == "S" and (self.refund_info.numeroPedidoVTEX is not None or self.refund_info.numeroPedidoVTEX != "")):
            kind_of_pix_sale_value = "tipo_venda_pix_ecommerce"
        else:
            raise ValueError("Sem correspondência para tipo de venda")

        id = self._get_field_id("tipo_venda_pix", "pix")
        self._add_field(FieldInfo(id, "Tipo de Venda PIX", kind_of_pix_sale_value))

    def _add_customer_name(self):

        id = self._get_field_id("nome_cliente", "pix")

        if self.refund_info.nomeCliente is None or len(self.refund_info.nomeCliente) < 5:
            self.refund_info.nomeCliente = "Sem nome de cliente"

        self._add_field(FieldInfo(id, "Nome do Cliente", self.refund_info.nomeCliente))

    def _add_nsu_tef(self):
        id = self._get_field_id("nsu_pix", "pix")
        valid_value = self.is_a_valid_nsu(self.refund_info.nsutef)
        self._add_field(FieldInfo(id, "NSU/TEF", valid_value))


class CardCustomFieldsCreator(BaseCustomFieldsCreator):
    def __init__(self, refund_info: EstornoLog, env: str = "prd") -> None:
        super().__init__(env)
        self.refund_info = refund_info

    def create_list(self):
        self._add_kind_of_card_sale()
        self._add_sale_date(self.refund_info.dataMovimento)
        self._add_ecommerce_number()
        self._add_enterprise()
        self._add_cancel_type("cancelamento_cancelamento_venda_em_cartao")
        self._add_refund_request_value()
        self._add_branch_number()
        self._add_sale_value()
        self._add_reason()
        self._add_constant_fields()

        self._add_pdv(self.refund_info)

    def _add_kind_of_card_sale(self):
        if self.refund_info.tipo == "N":
            kind_of_pix_sale_value = "tipo_venda_ecf"

        elif (self.refund_info.tipo == "S" and (self.refund_info.numeroPedidoVTEX is None or self.refund_info.numeroPedidoVTEX == "")):
            kind_of_pix_sale_value = "tipo_venda_poslio"

        elif self.refund_info.tipo == "S":
            kind_of_pix_sale_value = "tipo_venda_ecommerce"
        else:
            raise ValueError("Sem correspondência para tipo de venda")

        id = self._get_field_id("tipo_venda_cartao", "card")
        self._add_field(FieldInfo(id, "Tipo de Venda", kind_of_pix_sale_value))
    

    def _add_pdv(self, refund_info: EstornoLog):

        id = self._get_field_id("pdv", "common")

        payment_method_data = get_sale_payment_methods(refund_info.idEstorno)
        
        if isinstance(payment_method_data, ResultHandle):

            zen_cf_logger.warning("Sem dados de PDV porque não há dados para o finalizador")

            if refund_info.tipo == 'S':
                self._add_field(FieldInfo(id, "PDV", "999"))
                return
            else:
                zen_cf_logger.warning("Mas é possível encontrar entrá-lo na estorno_venda_log")
                pdv = refund_info.nsutef[:3]
        else:
            pdv = payment_method_data[0].pdv
        
        self._add_field(FieldInfo(id, "PDV", pdv))
    
