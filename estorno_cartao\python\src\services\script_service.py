import time
from datetime import datetime

class ScriptService():
    
        
    def __init__(self, ecopdv):
        self.ECOPDV = ecopdv
        super().__init__()     

    def sql_script_init(self):
        insert_ecopdv = self.ECOPDV
        return """INSERT INTO log_estorno.log_estorno ("data_inicio", "tipo") VALUES (current_date + current_time, '"""+insert_ecopdv+"""') RETURNING "id";"""

    def fail_eco_execute_sql_script(self, _id):
        id_ = _id
        return """UPDATE log_estorno.log_estorno SET "data_erro" = current_date + current_time WHERE "id" = '"""+id_+"""';"""

    def select_maxid(self):
        insert_ecopdv = self.ECOPDV
        datetoday = str(datetime.today()).split()[0]
        return """SELECT MAX(id) FROM log_estorno.log_estorno where tipo = '"""+insert_ecopdv+"""' AND CAST(DATA_INICIO AS DATE) = CAST('"""+datetoday+"""' AS DATE);"""

    def update(self, _id):
        id_ = _id
        return """UPDATE log_estorno.log_estorno SET "data_fim" = current_date + current_time WHERE "id" = '"""+id_+"""';"""

    def select_maxid_erro(self, erro_retorno):
        datetoday = str(datetime.today()).split()[0]
        return """SELECT MAX(id) FROM log_estorno.log_estorno where tipo = '"""+erro_retorno+"""' AND CAST(DATA_INICIO AS DATE) = CAST('"""+datetoday+"""' AS DATE);"""

    def update_data_erro(self, _id):
        return """UPDATE log_estorno.log_estorno SET "data_erro" = current_date + current_time WHERE "id" = '"""+_id+"""';"""



    

