from pathlib import Path
import sys
from typing import Dict
from datetime import datetime
from time import sleep
from urllib3.connectionpool import log as urlliblogger
import pandas as pd

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

if __name__ == "__main__":
    src_folder = local_execution_with_others_projects_modules(__file__, 4)

from services.logger import create_logger
from services.zendesk.api_requests import get_audits_for_ticket, get_ticket_info
from services.zendesk.zen_var import TICKET_SOLVED_STATUS
from Connections.search_db import SearchDb
from Connections.update_db import UpdateDb
from models.refund_log import estorno_venda_log_orm

log_level = 20
urlliblogger.setLevel(log_level)
if __name__ == '__main__': 
    ticket_veri_logger = create_logger('')
    ticket_veri_logger.setLevel(log_level)
else: 
    ticket_veri_logger = create_logger(__name__, without_handler = True)



if __name__ == "__main__":

    # 366348 cartão prd
    # 366169 pix prd
    # solved 
    # new 
    prd_env = "prd" 
    homo_env = "homo" 
    ticket_id_prd = 1462310 #ticket_id_prd = 1523112
    ticket_id_homo = 7780
    ticket = 7427

    from json import dumps
    ticket_general_info = get_ticket_info(ticket_id_homo, homo_env)

    json_bytes = dumps(ticket_general_info.data, indent=4)
    json_path = Path(__file__).parent / f"{ticket_id_homo}.json"
    json_path.write_text(json_bytes)

    successful ="reembolso para o cliente depende dos prazos estabelecidos"
    ticket_audits_info, headers_response = get_audits_for_ticket(ticket_id_prd, prd_env)
    json_bytes = dumps(ticket_audits_info.data, indent=4)
    if successful in json_bytes: print("success")
    else: print("not success")

    json_path = Path(__file__).parent / f"{ticket_id_prd}_audits.json"
    json_path.write_text(json_bytes)
    if ticket_audits_info.failure:
        print(ticket_audits_info.error_description)
        exit()

    audits = ticket_audits_info.data
    for event in audits["audits"]["events"]:

        if "plain_body" not in event: continue
        print("fim")


    print("fim")



