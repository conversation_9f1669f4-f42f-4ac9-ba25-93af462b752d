import requests
import json
from services.logger import create_logger

if __name__ == '__main__': 
    merc_pag_logger = create_logger('')
    merc_pag_logger.setLevel(10)
else: 
    merc_pag_logger = create_logger(__name__, without_handler = True)


class ServiceMercadoPago():
    
    def __init__(self, config: dict): 
        self.config = config
        self.__api_url_consulta = config['mercadopago']['api_consulta_url']
        self.__api_url_estorno = config['mercadopago']['api_estorno_url']
        self.__api_token_pos_pgm = config['mercadopago']['api_token_pos_pgm']
        self.__api_token_tef_pgm = config['mercadopago']['api_token_tef_pgm']
        self.__api_token_pos_ext = config['mercadopago']['api_token_pos_ext']
        self.__api_token_tef_ext = config['mercadopago']['api_token_tef_ext']
        super().__init__()

    def estornopix(self,nsutef:str, valorVenda:float,valorEstorno:float,tipo:str):
        
        dadosvenda = self.consultavenda(nsutef, tipo)
        
        if dadosvenda == -1:
            return -1
        
        print(dadosvenda.ok)
        
        dadosvenda = json.loads(dadosvenda.text)    
            
        
        status = (dadosvenda['status'])
        status_detail = (dadosvenda['status_detail'])
        amount_refunded = (dadosvenda['transaction_amount_refunded'])
        
        print(f"Estado do pagamento pix mercado pago: {status}")
        
        if status == 'refunded':
            return -2
        
        elif valorEstorno == amount_refunded:
            return -3
        
        elif status == 'cancelled':
            return -6
        
        
        elif (status == 'approved' and (status_detail == 'accredited' or status_detail=='partially_refunded')):
            estorno = self.realizarestorno(nsutef,valorVenda,valorEstorno,tipo)

            return estorno
        
        elif status == 'rejected':
            merc_pag_logger.info("Pagamento pix mercado pago rejeitado")
            return -20
        
        else:
            merc_pag_logger.info(f"Status de venda mercado pago não mapeado {status}")
            return -30
 

    def realizarestorno(self,nsutef:str, valorVenda:float, valorEstorno:float,tipo):
        url = self.__api_url_estorno.format(nsutef)
        
        if valorVenda > valorEstorno:
            print("Estorno Parcial")
            payload = json.dumps({
            "amount": valorEstorno
            })
        elif valorVenda == valorEstorno:
            print("Estorno Total")
            payload = {}
        
        elif valorVenda < valorEstorno:
            return -4
        
        if tipo == "tef_pgm":
            headers = {
            'Content-Type': 'application/json',
            'Authorization': self.__api_token_tef_pgm
            }
        elif tipo == "pos_pgm":
            headers = {
            'Content-Type': 'application/json',
            'Authorization': self.__api_token_pos_pgm
            }
        elif tipo == "tef_ext":
            headers = {
            'Content-Type': 'application/json',
            'Authorization': self.__api_token_tef_ext
            }
        elif tipo == "pos_ext":
            headers = {
            'Content-Type': 'application/json',
            'Authorization': self.__api_token_pos_ext
            }                
        
        response = requests.request("POST", url, headers=headers, data=payload)

        if not response.ok:

            print('Erro na requisicao de cosulta do pedido = {}'.format(response.status_code))
            print('Detalhes= {}'.format(response.text))
            error = json.loads(response.text)    

            message= error["message"]

            if message == "Payment too old to be refunded":
                return -11
            elif error["status"] == 400:
                return -6
        else:
            return -5    




    def consultavenda(self,nsutef,tipo):
        url = self.__api_url_consulta.format(nsutef)
        print('DEBUG\n')
        print(url)
        print('\nDEBUG')
        payload = ""
        if tipo == "tef_pgm":
            headers = {'Authorization': self.__api_token_tef_pgm}
        elif tipo == "pos_pgm":
            headers = {'Authorization': self.__api_token_pos_pgm}
        elif tipo == "tef_ext":
            headers = {'Authorization': self.__api_token_tef_ext}  
        elif tipo == "pos_ext":
            headers = {'Authorization': self.__api_token_pos_ext}        

        response = requests.request("GET", url, headers=headers, data=payload)
        
        if not response.ok:
            print('Erro na requisicao de cosulta do pedido = {}'.format(response.status_code))
            print('Detalhes= {}'.format(response.text))
            return -1
        
        return response




    