
import Connections.scripts as scripts 
from Connections.dao import DAO
from typing import Union

class SearchDb:

    def __init__(self):
        self.dao = DAO()


    def search_token(self, TIPO):
        """Método que recebe um objeto com os dados, obtem uma conexão, formata uma query específica, realiza consulta e fecha a conexão"""
        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()
        try:
           
            sql = scripts.SEARCH_TOKEN.format(TIPO) 
            cursor.execute(sql)

            result = cursor.fetchall()

            return result[0][0]
                

        except Exception as e:
            print(f"Não foi possível cadatrar acesso. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn)
    
    def search_id_pix(self, id_estorno):
        nome_do_servidor = "cosmos"
        try:
            conn = self.dao.create_connection(db=nome_do_servidor)
            cursor = conn.cursor()
            sql = scripts.SEARCH_ID_PIX.format(id_estorno)
        
            cursor.execute(sql)
            result = cursor.fetchone()
        
            return result[0]
        except Exception as e:
            print('Não foi possível consultar o ID_PIX')
            return None
    
    def search_reprocessamento(self ,tipo):
        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()

        if tipo == 'pos_pgm':
            REPROCESSAMENTO = scripts.SEARCH_REPROCESSAMENTO_PGM_POS
        elif tipo == 'tef_pgm':
            REPROCESSAMENTO = scripts.SEARCH_REPROCESSAMENTO_PGM_TEF
        elif tipo == 'pos_ext':
            REPROCESSAMENTO = scripts.SEARCH_REPROCESSAMENTO_EXT_POS
        elif tipo == 'tef_ext':
            REPROCESSAMENTO = scripts.SEARCH_REPROCESSAMENTO_EXT_TEF

        try:
            sql = REPROCESSAMENTO

            print(sql)
            cursor.execute(sql)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                
                results.append(dict(zip(columns, row)))
                
            if len(results) == 0:
                return None, 0
            
            return results, 1

        except Exception as e:
            print(f"Não foi possível cadatrar acesso. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn)


    def search_reprocessamento_retorno(self ,tipo):
        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()

        if tipo == 'cielo':
            REPROCESSAMENTO = scripts.SEARCH_REP_CIELO
        else:
            REPROCESSAMENTO = scripts.SEARCH_REP_GETNET
        
        try:
            sql = REPROCESSAMENTO

            print(sql)
            cursor.execute(sql)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                
                results.append(dict(zip(columns, row)))
                
            if len(results) == 0:
                return None, 0
            
            return results, 1

        except Exception as e:
            print(f"Não foi possível cadatrar acesso. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn)

    
    def search_duplicity(self ,valor_estornar,id_devtroca_cab, id_estorno):
        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()

        QUERY = scripts.SEARCH_DUPLICIDADES.format(valor_estornar,id_estorno, id_devtroca_cab)
        
        try:

            sql = QUERY

            print(sql)
            cursor.execute(sql)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
                
            if len(results) == 0:
                return None, 0
            
            return results, 1

        except Exception as e:
            print(f"Não foi possível cadatrar acesso. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn)     



    def search_duplicity_mov(self ,id_devtroca_cab):
        #TODO Verificar se a quantidade de linhas de movimento presente na estorno_venda estão iguais as linhas da venda da tabela devtroca_cab
        #Assim identificando casos onde dados duplicados vieram parar na tabela do RPA
        #Após definir algoritmo para retornar lista com id_estorno das duplicatas para serem puladas do processamento, ou lista vazia

        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()

        QUERY = scripts.SEARCH_MOVIMENTOS_BY_ID_DEVTROCA.format(id_devtroca_cab)
        QUERY_TROCACAB = scripts.SEARCH_COUNT_MOV_DEVTROCA.format(id_devtroca_cab)
        
        try:
            #obter os movimentos
            sql = QUERY

            cursor.execute(sql)

            columns = [column[0] for column in cursor.description]
            results_mov = []
            for row in cursor.fetchall():
                results_mov.append(dict(zip(columns, row)))
            
            #obter a quantidade presente na devtroca_cab

            sql = QUERY_TROCACAB
            
            cursor.execute(sql)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            qtd_trocacab = results[0]['LINHAS_MOVIMENTO']
            
            return identifie_duplicities(qtd_trocacab, results_mov)

            

        except Exception as e:
            print(f"Não foi possível encontrar movimento. Detalhe: {e}")
            return -5
        finally:
            self.dao.close(conn)       


    def search_refund_by_cab_approach(self, auth_code: str, id_cab: str, branch_num: Union[str, int], sale_date: str):

        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()

        branch_num = int(branch_num) if isinstance(branch_num, str) else branch_num

        QUERY = scripts.SEARCH_BY_CAB_APPROACH.format(auth_code, id_cab, branch_num, sale_date)
        
        try:

            cursor.execute(QUERY)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            

            if len(results) == 0:
                return None, 0

            return results, 1

            

        except Exception as e:
            print(f"Não foi possível encontrar movimento. Detalhe: {e}")
            return -5
        finally:
            self.dao.close(conn)

    def search_refund_by_refund_id(self, refund_id: Union[str, int]):

        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()

        refund_id = int(refund_id) if isinstance(refund_id, str) else refund_id

        QUERY = scripts.SEARCH_BY_REFUND_ID.format(refund_id)
        
        try:

            cursor.execute(QUERY)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            

            if len(results) == 0:
                return None, 0

            return results, 1

        except Exception as e:
            print(f"Não foi possível encontrar atendimento de estorno. Detalhe: {e}")
            return None, -5
        finally:
            self.dao.close(conn) 
    

    def search_refund_with_fluig_but_without_zendesk(self, initial_date: str, final_date: str):
        """Buscar quais são os pedidos de estorno que criaram solicitações no fluig,
        mas não criaram ticket no zendesk

        Args:
            initial_date (str): Padrão da data (dd/mm/aaaa)
            final_date (str): A data final é aberta, ou seja, se eu verificar domento o dia '16/09/2024'
            a data final deve ser '17/09/2024'. 
        """

        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()


        QUERY = scripts.SEARCH_REFUND_WITH_FLUIG_BUT_WITHOUT_ZENDESK.format(initial_date, final_date)
        
        try:

            cursor.execute(QUERY)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            

            if len(results) == 0:
                return None, 0

            return results, 1


        except Exception as e:
            print(f"Não foi possível encontrar movimento. Detalhe: {e}")
            return -5
        finally:
            self.dao.close(conn)
    
    def search_not_solved_ticket_in_duality_stage(self):

        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()


        QUERY = scripts.SEARCH_NOT_SOLVED_TICKET_IN_DUALITY
        
        try:

            cursor.execute(QUERY)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            

            if len(results) == 0:
                return None, 0

            return results, 1


        except Exception as e:
            print(f"Não foi possível encontrar movimento. Detalhe: {e}")
            return -5
        finally:
            self.dao.close(conn)

    def search_not_solved_after_migration(self, migration_date: str):

        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()


        QUERY = scripts.SEARCH_NOT_SOLVED_TICKET_AFTER_MIGRATION.format(migration_date)
        
        try:

            cursor.execute(QUERY)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            

            if len(results) == 0:
                return None, 0

            return results, 1


        except Exception as e:
            print(f"Não foi possível encontrar movimento. Detalhe: {e}")
            return -5
        finally:
            self.dao.close(conn) 


    def search_refund_by_refund_id_in_estorno_venda(self, refund_id: Union[str, int]):

        nome_do_servidor = "cosmos"
        conn = self.dao.create_connection(db=nome_do_servidor)
        cursor = conn.cursor()

        refund_id = int(refund_id) if isinstance(refund_id, str) else refund_id

        QUERY = scripts.SEARCH_BY_REFUND_ID_IN_ESTORNO_VENDA.format(refund_id)
        
        try:

            cursor.execute(QUERY)

            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            

            if len(results) == 0:
                return None, 0

            return results, 1

            

        except Exception as e:
            print(f"Não foi possível encontrar pedido de estorno na estorno venda. Detalhe: {e}")
            return -5
        finally:
            self.dao.close(conn) 

def identifie_duplicities(qtd_trocacab, movimentos):
    #validar as quantidades
    if int(qtd_trocacab) == len(movimentos): return []

    #obter os id_estorno de duplicatas
    movimentos_para_processar = []
    ids_duplicados = []
    first = True
    for mov in movimentos:
        nsu_tef = mov['NSU_TEF']
        nsu_host = mov['NSU_HOST']
        

        id_mov = (nsu_tef, nsu_host)
        if first:
            movimentos_para_processar.append(id_mov)
            first = False
            continue

        if id_mov not in movimentos_para_processar:
            ids_duplicados.append(mov['ID_ESTORNO'])
        else:
            movimentos_para_processar.append(id_mov)

    
    if ids_duplicados != []: return ids_duplicados

    #capturar duplicadas valores repertidos
    movimentos_para_processar = []
    count = 0
    for mov in movimentos:
        if count == qtd_trocacab:break
        
        movimentos_para_processar.append(mov['ID_ESTORNO'])
        count +=1

    return [mov['ID_ESTORNO'] for mov in movimentos if mov['ID_ESTORNO'] not in movimentos_para_processar]




if __name__ == '__main__':
    sea = SearchDb()
    res = sea.search_refund_by_refund_id_in_estorno_venda(326084)
    print(res)
