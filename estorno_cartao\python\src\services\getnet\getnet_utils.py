from pathlib import Path
import sys
from dataclasses import dataclass, asdict
from datetime import datetime

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder


src_folder = local_execution_with_others_projects_modules(__file__, 3)

from models.refund_log import estorno_venda_log_orm
from services.logger import create_logger
from Config.database import query_all_db
from services.getnet_service import ServiceGetnet
import Config.vars as global_vars
from services.telegram_bot_service import *
from models.refund_log import EstornoLog
import utils


if __name__ == '__main__': 
    getnet_utils_logger = create_logger('')
    getnet_utils_logger.setLevel(10)
else: 
    getnet_utils_logger = create_logger(__name__, without_handler = True)


def track_getnet_refund_request()-> global_vars.ResultHandle:
    """Quais são os pedidos de estorno em andamento na Getnet

    Returns:
        global_vars.ResultHandle: quando sucesso List[EstornoLog]
    """    

    TRACK_GETNET_REFUND_REQUESTS = """
                    SELECT *
                    FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG ev_log (nolock)
                    WHERE 
                    ev_log.STATUS like '%GETNET%'
                    ORDER BY DATA_CADASTRO ASC
                    """

    try:
        getnet_refund_requests = query_all_db(TRACK_GETNET_REFUND_REQUESTS)
        result = list(map(estorno_venda_log_orm, getnet_refund_requests))

    except Exception as e:
        msg = "Erro ao buscar estorno em andamento da Getnet"
        getnet_utils_logger.error(msg)
        getnet_utils_logger.error(f"Detalhes do erro: {e}")
        return global_vars.ResultHandle.Fail(f"{msg} | {e}")

    if len(getnet_refund_requests) == 0:
        msg = "Não foi encontrado nenhum estorno em andamento na Getnet"
        getnet_utils_logger.info(msg)
        return global_vars.ResultHandle.Waiting(error=msg)

    
    return global_vars.ResultHandle.Ok(result)

@dataclass
class QueryCancelTransactionRequest:
    branch: str
    terminal: str
    date: str
    nsu: str

def find_getnet_refund_protocol_with_sale_info(refund: EstornoLog)->global_vars.ResultHandle:

    branch_info_in_acquire, _ = utils.get_branch_id_in_acquirer(refund, global_vars.GETNET_ACQUIRE_SCOPE_CODE)

    if isinstance(branch_info_in_acquire, global_vars.ResultHandle):
        telegram_bot_sendtext(branch_info_in_acquire.error_description)
        return branch_info_in_acquire


    query_cancel = QueryCancelTransactionRequest(branch = branch_info_in_acquire.establishment,
                                                terminal = branch_info_in_acquire.terminal,
                                                date = datetime.strptime(refund.dataMovimento , '%Y-%m-%d').strftime("%d%m%Y"),
                                                nsu = refund.nsuhost)

    getnet_service = ServiceGetnet(utils.get_config())
    venda = getnet_service.verify_cancelation_request_by_sale_info(query_cancel, asdict(refund))

    if venda['protocoloGetnet'] is None:
        msg = F"URGENTE: Sem protocolo Getnet para id_estorno: {venda['idEstorno']} Filial: {venda['filial']}; cupom: {venda['numeroCupom']}; COO: {venda['numeroCoo']}"
        telegram_bot_sendtext(msg)
        return global_vars.ResultHandle.Fail(msg)
    
    return global_vars.ResultHandle.Ok(venda)

if __name__ == '__main__': 

    x = track_getnet_refund_request()

    print("fim")