##SEARCHS##



SEARCH_TOKEN = "SELECT TOKEN FROM COSMOSRPA..ACESSOS_CIELO WHERE TIPO = '{}'"

SEARCH_ID_PIX = "SELECT ID_PIX FROM COSMOSRPA..ESTORNO_VENDA WHERE ID_ESTORNO = '{}'"

SEARCH_DUPLICIDADES = ''' SELECT * FROM cosmosrpa..ESTORNO_VENDA_LOG
						WHERE 
						VALOR_ESTORNO = {}
						AND ID_ESTORNO = {}
						AND ID_DEVTROCA_CAB = '{}' '''

SEARCH_MOVIMENTOS_BY_ID_DEVTROCA = ''' SELECT * FROM CosmosRPA.dbo.ESTORNO_VENDA nolock
where ID_DEVTROCA_CAB = {}'''


SEARCH_COUNT_MOV_DEVTROCA = '''
 
SELECT

count(TROCDEV.ID_DEVTROCA_CAB) as LINHAS_MOVIMENTO

FROM COSMOSFL..MVENT_TRCDEV_CAB TROCDEV WITH (NOLOCK)

LEFT JOIN cosmos_v14b..FILIAL FILI_DEST WITH (NOLOCK) ON TROCDEV.MVTC_CD_FILIAL_MOV = FILI_DEST.FILI_CD_FILIAL

LEFT JOIN cosmos_v14b..FILIAL FILI_ORIG WITH (NOLOCK) ON TROCDEV.MVTC_NR_FIL = FILI_ORIG.FILI_CD_FILIAL

LEFT JOIN COSMOSFL..MVENT_TRCDEV_SALDO_FINALIZ SALDO WITH (NOLOCK) ON TROCDEV.ID_DEVTROCA_CAB = SALDO.ID_DEVTROCA_CAB

LEFT JOIN COSMOSFL..FLZ_DEV FLZ WITH(NOLOCK)

ON

(

TROCDEV.MVTC_DT_MOV = FLZ.FLDE_DT_MOV

AND TROCDEV.MVTC_CD_FILIAL_MOV = FLZ.FLDE_CD_FILIAL_MOV

AND TROCDEV.MVTC_NR_ECF_MOV = FLZ.FLDE_NR_ECF_MOV

AND SALDO.ID_MVENT_TRCDEV_SALDO_FINALIZ = FLZ.FLDE_CT_FLZ_DEV

)

LEFT JOIN COSMOSMKT..FIDELIZ_CLI CLI WITH(NOLOCK)

ON(

TROCDEV.NUMERO_CARTAO_SEMPRE = CLI.FICL_SQ_CLIENTE

)

LEFT JOIN COSMOSFL..MVENT_TRCDEV_DIGITACAO_POS DPOS WITH(NOLOCK)

ON (

SALDO.ID_DEVTROCA_CAB = DPOS.ID_DEVTROCA_CAB

AND SALDO.CT_VDA = DPOS.SEQUENCIAL

AND SALDO.ID_FLZ_VENDA = DPOS.SEQ_FINALIZADORA

)

LEFT JOIN COSMOS_V14B..CARTAO_CREDITO CC WITH(NOLOCK)

ON (DPOS.CODIGO_BANDEIRA = CC.CACR_SQ_CARTAO)

LEFT JOIN COSMOSFL..MVENT_TRCDEV_DIGITACAO_CUPOM_DIGITADO DFLZ WITH(NOLOCK)

ON (TROCDEV.ID_DEVTROCA_CAB = DFLZ.ID_DEVTROCA_CAB)

WHERE TROCDEV.ID_DEVTROCA_CAB in ({})

'''



SEARCH_REP_CIELO = '''SELECT 
					FILIAL_ORIGEM as filial,
					DATA_MOVIMENTO as dataVenda,
					CODIGO_AUTORIZAÇÂO as codigoAutorizacao,
					NSU_HOST as nsu,
					NUMERO_CUPOM as numeroCupom,
					NUMERO_COO as numeroCoo,
					VALOR_CUPOM as valorVenda,
					VALOR_ESTORNO as valorEstorno,
					PDV_DEVOLUCAO as ecf,
					STATUS as status,
					NOME_EMPRESA as nomeEmpresa,
					PROTOCOLO_CIELO as protocoloCielo,
					ID_ESTORNO_CAB as arquivo,
					ID_INST_BPM as instancia

					from cosmosrpa..ESTORNO_VENDA_LOG
					where STATUS = 'STATUS_WAIT_CIELO_REP' '''

SEARCH_REP_GETNET = '''SELECT 
					FILIAL_ORIGEM as filial,
					DATA_MOVIMENTO as dataVenda,
					CODIGO_AUTORIZAÇÂO as codigoAutorizacao,
					NSU_HOST as nsu,
					NUMERO_CUPOM as numeroCupom,
					NUMERO_COO as numeroCoo,
					VALOR_CUPOM as valorVenda,
					VALOR_ESTORNO as valorEstorno,
					PDV_DEVOLUCAO as ecf,
					STATUS as status,
					NOME_EMPRESA as nomeEmpresa,
					PROTOCOLO_GETNET as protocoloGetnet,
					ID_ESTORNO_CAB as arquivo,
					ID_INST_BPM as instancia

					from cosmosrpa..ESTORNO_VENDA_LOG
					where STATUS = 'EM ANDAMENTO GETNET_REP' '''



SEARCH_REPROCESSAMENTO_PGM_POS = '''SELECT DISTINCT FILIAL_ORIGEM codigoFilialOrigem, 
																		ISNULL(CNPJ_FILIAL_ORIGEM, '') cnpjFilialOrigem,
																		FILIAL_DESTINO codigoFilialDestino,
																		ISNULL(CNPJ_FILIAL_DESTINO, '') cnpjFilialDestino,
																		ISNULL(NUMERO_CUPOM, '') numeroCupom,
																		ISNULL(NUMERO_COO, '') numeroCoo,
																		PDV_DEVOLUCAO pdvDevolucao,
																		ISNULL(LTRIM(RTRIM(NOME_CLIENTE)), '') nomeCliente,
																		ISNULL(NUMERO_VTEX, '') numeroPedidoVTEX,
																		RTRIM(ISNULL(NUMERO_DELIVERY, '')) numeroPedidoDelivery, 																		 
																		ISNULL(DATA_MOVIMENTO, '') dataMovimento,
																		VALOR_CUPOM valorVenda,
																		VALOR_ESTORNO valorEstorno,
																		ISNULL(DATA_DEVOLUCAO, '') dataDevolucao,
																		ISNULL(NSU_TEF, '') codigoNSUTEF,
																		ISNULL(NSU_HOST, '') codigoNSUHOST,
																		ISNULL(RTRIM(BANDEIRA), '') bandeira,
																		ISNULL(TELEFONE_CLEINTE, '') telefoneCliente,
																		ISNULL(EMAIL_CLEINTE, '') emailCliente,
																		NUMERO_PRE_VENDA numeroPreVenda,
																		ISNULL(E_PLANO_ASSINATURA, '') planoAssinatura,
																		ISNULL(CANAL_VENDAS, '') canalVendas,
																		ISNULL(NUMERO_CARTAO_SEMPRE, '') cartaoSempre,
																		ISNULL(FLDE_QT_PAR_CAR, '') quantParcelas,																																			
																		ISNULL(FLDE_TP_POS, '') tipo,
																		ISNULL(E_PAGAMENTO_UNIFICADO, '') flagPagamentoUnificado,
																		ISNULL(NSU_POS, '') nsuPos,
																		ID_DEVTROCA_CAB idTrocaCab,
																		ISNULL(COO_PAGAMENTO_UNIFICADO, '') cooPagUni,
																		ID_ESTORNO idEstorno,
																		STATUS statusVenda,
																		ID_INST_BPM instancia
																																																																				
																FROM COSMOSRPA..ESTORNO_VENDA_LOG  
																WHERE 
																FLDE_TP_POS = 'S'
																and NOME_EMPRESA = 'Pague Menos'
																and STATUS = 'reprocessamento' '''

SEARCH_REPROCESSAMENTO_PGM_TEF = '''SELECT DISTINCT FILIAL_ORIGEM codigoFilialOrigem, 
																		ISNULL(CNPJ_FILIAL_ORIGEM, '') cnpjFilialOrigem,
																		FILIAL_DESTINO codigoFilialDestino,
																		ISNULL(CNPJ_FILIAL_DESTINO, '') cnpjFilialDestino,
																		ISNULL(NUMERO_CUPOM, '') numeroCupom,
																		ISNULL(NUMERO_COO, '') numeroCoo,
																		PDV_DEVOLUCAO pdvDevolucao,
																		ISNULL(LTRIM(RTRIM(NOME_CLIENTE)), '') nomeCliente,
																		ISNULL(NUMERO_VTEX, '') numeroPedidoVTEX,
																		RTRIM(ISNULL(NUMERO_DELIVERY, '')) numeroPedidoDelivery, 																		 
																		ISNULL(DATA_MOVIMENTO, '') dataMovimento,
																		VALOR_CUPOM valorVenda,
																		VALOR_ESTORNO valorEstorno,
																		ISNULL(DATA_DEVOLUCAO, '') dataDevolucao,
																		ISNULL(NSU_TEF, '') codigoNSUTEF,
																		ISNULL(NSU_HOST, '') codigoNSUHOST,
																		ISNULL(RTRIM(BANDEIRA), '') bandeira,
																		ISNULL(TELEFONE_CLEINTE, '') telefoneCliente,
																		ISNULL(EMAIL_CLEINTE, '') emailCliente,
																		NUMERO_PRE_VENDA numeroPreVenda,
																		ISNULL(E_PLANO_ASSINATURA, '') planoAssinatura,
																		ISNULL(CANAL_VENDAS, '') canalVendas,
																		ISNULL(NUMERO_CARTAO_SEMPRE, '') cartaoSempre,
																		ISNULL(FLDE_QT_PAR_CAR, '') quantParcelas,																																			
																		ISNULL(FLDE_TP_POS, '') tipo,
																		ISNULL(E_PAGAMENTO_UNIFICADO, '') flagPagamentoUnificado,
																		ISNULL(NSU_POS, '') nsuPos,
																		ID_DEVTROCA_CAB idTrocaCab,
																		ISNULL(COO_PAGAMENTO_UNIFICADO, '') cooPagUni,
																		ID_ESTORNO idEstorno,
																		STATUS statusVenda,
																		ID_INST_BPM instancia
																																																																				
																FROM COSMOSRPA..ESTORNO_VENDA_LOG  
																WHERE 
																FLDE_TP_POS = 'N'
																and NOME_EMPRESA = 'Pague Menos'
																and STATUS = 'reprocessamento' ''' 

SEARCH_REPROCESSAMENTO_EXT_TEF = '''SELECT DISTINCT FILIAL_ORIGEM codigoFilialOrigem, 
																		ISNULL(CNPJ_FILIAL_ORIGEM, '') cnpjFilialOrigem,
																		FILIAL_DESTINO codigoFilialDestino,
																		ISNULL(CNPJ_FILIAL_DESTINO, '') cnpjFilialDestino,
																		ISNULL(NUMERO_CUPOM, '') numeroCupom,
																		ISNULL(NUMERO_COO, '') numeroCoo,
																		PDV_DEVOLUCAO pdvDevolucao,
																		ISNULL(LTRIM(RTRIM(NOME_CLIENTE)), '') nomeCliente,
																		ISNULL(NUMERO_VTEX, '') numeroPedidoVTEX,
																		RTRIM(ISNULL(NUMERO_DELIVERY, '')) numeroPedidoDelivery, 																		 
																		ISNULL(DATA_MOVIMENTO, '') dataMovimento,
																		VALOR_CUPOM valorVenda,
																		VALOR_ESTORNO valorEstorno,
																		ISNULL(DATA_DEVOLUCAO, '') dataDevolucao,
																		ISNULL(NSU_TEF, '') codigoNSUTEF,
																		ISNULL(NSU_HOST, '') codigoNSUHOST,
																		ISNULL(RTRIM(BANDEIRA), '') bandeira,
																		ISNULL(TELEFONE_CLEINTE, '') telefoneCliente,
																		ISNULL(EMAIL_CLEINTE, '') emailCliente,
																		NUMERO_PRE_VENDA numeroPreVenda,
																		ISNULL(E_PLANO_ASSINATURA, '') planoAssinatura,
																		ISNULL(CANAL_VENDAS, '') canalVendas,
																		ISNULL(NUMERO_CARTAO_SEMPRE, '') cartaoSempre,
																		ISNULL(FLDE_QT_PAR_CAR, '') quantParcelas,																																			
																		ISNULL(FLDE_TP_POS, '') tipo,
																		ISNULL(E_PAGAMENTO_UNIFICADO, '') flagPagamentoUnificado,
																		ISNULL(NSU_POS, '') nsuPos,
																		ID_DEVTROCA_CAB idTrocaCab,
																		ISNULL(COO_PAGAMENTO_UNIFICADO, '') cooPagUni,
																		ID_ESTORNO idEstorno,
																		STATUS statusVenda,
																		ID_INST_BPM instancia
																																																																				
																FROM COSMOSRPA..ESTORNO_VENDA_LOG  
																WHERE 
																FLDE_TP_POS = 'N'
																and NOME_EMPRESA = 'Extrafarma'
																and STATUS = 'reprocessamento' '''

SEARCH_REPROCESSAMENTO_EXT_POS = '''SELECT DISTINCT FILIAL_ORIGEM codigoFilialOrigem, 
																		ISNULL(CNPJ_FILIAL_ORIGEM, '') cnpjFilialOrigem,
																		FILIAL_DESTINO codigoFilialDestino,
																		ISNULL(CNPJ_FILIAL_DESTINO, '') cnpjFilialDestino,
																		ISNULL(NUMERO_CUPOM, '') numeroCupom,
																		ISNULL(NUMERO_COO, '') numeroCoo,
																		PDV_DEVOLUCAO pdvDevolucao,
																		ISNULL(LTRIM(RTRIM(NOME_CLIENTE)), '') nomeCliente,
																		ISNULL(NUMERO_VTEX, '') numeroPedidoVTEX,
																		RTRIM(ISNULL(NUMERO_DELIVERY, '')) numeroPedidoDelivery, 																		 
																		ISNULL(DATA_MOVIMENTO, '') dataMovimento,
																		VALOR_CUPOM valorVenda,
																		VALOR_ESTORNO valorEstorno,
																		ISNULL(DATA_DEVOLUCAO, '') dataDevolucao,
																		ISNULL(NSU_TEF, '') codigoNSUTEF,
																		ISNULL(NSU_HOST, '') codigoNSUHOST,
																		ISNULL(RTRIM(BANDEIRA), '') bandeira,
																		ISNULL(TELEFONE_CLEINTE, '') telefoneCliente,
																		ISNULL(EMAIL_CLEINTE, '') emailCliente,
																		NUMERO_PRE_VENDA numeroPreVenda,
																		ISNULL(E_PLANO_ASSINATURA, '') planoAssinatura,
																		ISNULL(CANAL_VENDAS, '') canalVendas,
																		ISNULL(NUMERO_CARTAO_SEMPRE, '') cartaoSempre,
																		ISNULL(FLDE_QT_PAR_CAR, '') quantParcelas,																																			
																		ISNULL(FLDE_TP_POS, '') tipo,
																		ISNULL(E_PAGAMENTO_UNIFICADO, '') flagPagamentoUnificado,
																		ISNULL(NSU_POS, '') nsuPos,
																		ID_DEVTROCA_CAB idTrocaCab,
																		ISNULL(COO_PAGAMENTO_UNIFICADO, '') cooPagUni,
																		ID_ESTORNO idEstorno,
																		STATUS statusVenda,
																		ID_INST_BPM instancia
																																																																				
																FROM COSMOSRPA..ESTORNO_VENDA_LOG  
																WHERE 
																FLDE_TP_POS = 'S'
																and NOME_EMPRESA = 'Extrafarma'
																and STATUS = 'reprocessamento' '''

SEARCH_BY_CAB_APPROACH = """
SELECT *

FROM CosmosRPA..ESTORNO_VENDA_LOG (nolock)
WHERE 

CODIGO_AUTORIZAÇÂO =  '{}'
AND ID_ESTORNO_CAB = '{}'
AND FILIAL_ORIGEM = '{}'
AND CONVERT(DATE, DATA_MOVIMENTO) = CONVERT(DATE, '{}')
"""


SEARCH_BY_REFUND_ID = """
SELECT *
FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG (nolock)
WHERE 
ID_ESTORNO = {}
"""


SEARCH_BY_REFUND_ID_IN_ESTORNO_VENDA = """
SELECT *
FROM CosmosRPA.dbo.ESTORNO_VENDA (nolock)
WHERE 
ID_ESTORNO = {}
"""


SEARCH_REFUND_WITH_FLUIG_BUT_WITHOUT_ZENDESK = """
SELECT * FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG ev_log (nolock)
WHERE 
ev_log.DATA_CADASTRO >= CONVERT(datetime, '{}', 103) 
and ev_log.DATA_CADASTRO < CONVERT(datetime, '{}', 103)
AND ev_log.MOTIVO not LIKE '%V2%'
AND ev_log.ID_INST_BPM is not null
AND (ev_log.NUMERO_DELIVERY is null OR ev_log.NUMERO_DELIVERY like '')
ORDER BY ev_log.DATA_CADASTRO  desc
"""

SEARCH_NOT_SOLVED_TICKET_IN_DUALITY = """
SELECT * FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG ev_log (nolock)
WHERE 
-- Início da transição para o zendesk
ev_log.DATA_CADASTRO >= CONVERT(datetime, '16/09/2024', 103) 
AND ev_log.NUMERO_DELIVERY is not null
AND ev_log.NUMERO_DELIVERY <> ''
AND ev_log.STATUS_BPM is null
ORDER BY ev_log.DATA_CADASTRO DESC
"""


SEARCH_NOT_SOLVED_TICKET_AFTER_MIGRATION = """
SELECT * FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG ev_log (nolock)
WHERE 
-- Dia da migração zendesk
ev_log.DATA_CADASTRO >= CONVERT(datetime, '{}', 120) 
AND ev_log.ID_INST_BPM is not null
AND ev_log.STATUS_BPM is null
ORDER BY ev_log.DATA_CADASTRO DESC
"""

SEARCH_REFUNDS_FOR_SAME_TRANSACTION = """
SELECT top 1 ID_DEVTROCA_CAB, ID_ESTORNO, DATA_DEVOLUCAO 
FROM CosmosRPA.dbo.ESTORNO_VENDA_LOG ev_log (nolock)
WHERE 
ev_log.FILIAL_ORIGEM = {branch_num}
and ev_log.CODIGO_AUTORIZAÇÂO = '{auth_code}'
ORDER BY ev_log.DATA_DEVOLUCAO DESC
"""

##UPDATES##
UPDATE_TOKEN = "UPDATE COSMOSRPA..ACESSOS_CIELO SET TOKEN = '{}',DATA_ATUALIZACAO = GETDATE() WHERE TIPO = '{}'"

UPDATE_STATUS_CIELO = "UPDATE COSMOSRPA..ESTORNO_VENDA_LOG SET STATUS = '{}',PROTOCOLO_CIELO = '{}' WHERE ID_DEVTROCA_CAB = '{}'"

UPDATE_STATUS_GETNET = "UPDATE COSMOSRPA..ESTORNO_VENDA_LOG SET STATUS = '{}',PROTOCOLO_GETNET = '{}' WHERE ID_DEVTROCA_CAB = '{}'"

UPDATE_STATUS_ITAU = "UPDATE COSMOSRPA..ESTORNO_VENDA_LOG SET STATUS = '{}' WHERE ID_DEVTROCA_CAB = '{}'"

GENERAL_REFUND_PROCESSMENT_STEP_UPDATE = """
UPDATE CosmosRPA.dbo.ESTORNO_VENDA_LOG 
SET MOTIVO = '{motive}',
STATUS = '{status}',
DATA_ALTERACAO = GETDATE(),
DETALHAMENTO = '{detail}'
WHERE 
ID_ESTORNO = {refund_id}
"""

REFUND_PROCESSMENT_STEP_UPDATE_WITH_ACQUIRENTE_RESPONSE_DATE = """
UPDATE CosmosRPA.dbo.ESTORNO_VENDA_LOG 
SET MOTIVO = '{motive}',
STATUS = '{status}',
DATA_ALTERACAO = GETDATE(),
DETALHAMENTO = '{detail}',
DATA_RESP_ADQUIRENTE = '{acquirente_response_date}'
WHERE 
ID_ESTORNO = {refund_id}
"""

REGISTER_TICKET_ZENDESK = """
UPDATE CosmosRPA.dbo.ESTORNO_VENDA_LOG 
SET ID_INST_BPM = {},
CRIACAO_DO_TICKET = GETDATE(),
ETAPA_CRIACAO_TICKET = '{}'
WHERE ID_ESTORNO = {}
"""

REGISTER_TICKET_FINALIZATION = """
UPDATE CosmosRPA.dbo.ESTORNO_VENDA_LOG
SET STATUS_BPM = ?,
STATUS = ?,
DETALHAMENTO = ?,
FIM_ATENDIMENTO_MANUAL = ?
WHERE ID_ESTORNO = ?
"""


CHANGE_STATUS_AND_DETALHAMENTO = """
UPDATE CosmosRPA.dbo.ESTORNO_VENDA_LOG
SET STATUS = ?,
DETALHAMENTO = ?
WHERE ID_ESTORNO = ?
"""


CHANGE_STATUS_AND_DETALHAMENTO_STATUS_BPM = """
UPDATE CosmosRPA.dbo.ESTORNO_VENDA_LOG
SET STATUS = ?,
DETALHAMENTO = ?,
STATUS_BPM = ?
WHERE ID_ESTORNO = ?
"""





