import requests
import json
import re
from pathlib import Path
import sys

def local_execution_with_others_projects_modules(current_file: str, directory_to_go_back: int = 1) -> Path:
    """
    If in a local execution the current module uses resources from another \n
    module that is in the same project i.e.(this module is in the same \n
    directory as the other module) and they aren't in the same level. \n
    So you need to add the common dictory between the modules to the Paths of execution \n
    
    Args:
        `current_file` (str): file path who calls the other modules

    Returns:
        `Path`: common directory Path
    """
    
    src_folder = Path(current_file.split(":")[0].upper() + ":" + current_file.split(":")[1])
    for _ in range(directory_to_go_back): src_folder = src_folder.parent
    if src_folder.__str__() not in sys.path: sys.path.append(str(src_folder))
    if Path.cwd().__str__() not in sys.path: sys.path.append(Path.cwd().__str__())

    return src_folder

src_folder = local_execution_with_others_projects_modules(__file__, 2)

import utils
from services.logger import create_logger

if __name__ == '__main__': 
    veloce_logger = create_logger('')
    veloce_logger.setLevel(10)
else: 
    veloce_logger = create_logger(__name__, without_handler = True)


class LioService():

    def __init__(self, config):
        self.config = config
        self.__api_base_url = config['api']['lio']['api_base_url']   
        self.login = config['api']['lio']['login']  
        self.senha = config['api']['lio']['senha']                      
        super().__init__()   

   

    def get_token(self):
        url = self.__api_base_url + self.config['api']['lio']['acessar']
        payload = json.dumps({
            "login":self.login,
            "senha":self.senha
        })

        print(payload)

        headers = {
            'Content-Type':'application/json'
        }
        response = requests.request("POST", url, headers = headers, data = payload)
        
        return response.json()['result']['token']

    
    def consultar_vendas(self ,data, cnpj ,cupom, token):
        
        url = self.__api_base_url + self.config['api']['lio']['get_venda'] 
        url = url.format(data,data,cnpj,token) 
        print("\n\n\n#######DEBUG########")
        print(url)
        print("#######DEBUG########\n\n\n")
        
        response = requests.request("GET", url)

        if not response.ok:
            if response.status_code ==  404: return -1
            if response.status_code == 400: return -1
            response_list = response.json()
            raise ValueError('Erro na requisicao: status {}'.format(response.status_code))

        response_list = response.json()

        print(response_list)

        dados = None
        count = 0

        for i in response_list['result']:            
            if i['numero'] == cupom:
                dados = response_list['result'][count]
                continue
            if len(response_list['result'])==count + 1:
                dados = -1
            count = count + 1

        return dados


def extract_ecnumber_getnet_pos(json_response: str): 
    
    ec_matcher = re.search(r"(?<=\"ecNumber\":\")[\d]+", json_response)

    if ec_matcher is None:
        veloce_logger.error("Não foi encontrado cód. estabelecimento para POS getnet")
        raise ValueError()
    
    return ec_matcher.group(0).lstrip('0')
    

def extract_getnet_auth_code_in_pos(json_response: str):

    auth_code_matcher = re.search(r"(?<=authorizationCode\=)[\d]+", json_response)

    if auth_code_matcher is None:
        veloce_logger.error("Não foi encontrado cód. de autorização para POS getnet")
        raise ValueError()
    
    return auth_code_matcher.group(0)

def extract_getnet_external_nsu_in_pos(json_response: str):

    nsu_matcher = re.search(r"(?<=nsu\=)[\d]+", json_response)

    if nsu_matcher is None:
        veloce_logger.error("Não foi encontrado NSU para POS getnet")
        raise ValueError()
    
    nsu_external = nsu_matcher.group(0)
    diference = len(nsu_external) - 9
    if diference > 9:
        nsu_external = nsu_external[diference:]

    return nsu_external

def extract_getnet_terminal_in_pos(json_response: str):

    terminal_matcher = re.search(r"(?<=\"terminal\":\")[\d]+", json_response)

    if terminal_matcher is None:
        veloce_logger.error("Não foi encontrado terminal para POS getnet")
        raise ValueError()

    return terminal_matcher.group(0)



if __name__ == "__main__":


    app_config = utils.get_config()  

    veloce = LioService(app_config)

    token = veloce.get_token()
    #"06/04/2025", '06626253084612', 36832,
    sale = veloce.consultar_vendas("06/04/2025", '06626253084312', 36832, token)
    x = float(sale['pagamentos'][0][0]['valor'])
    pagamento = sale['pagamentos'][0][0]['json_pagamento']
    jsonpag = json.loads(pagamento)

    print("fim")




         
    